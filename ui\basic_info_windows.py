# -*- coding: utf-8 -*-
"""
基本信息管理窗口
"""

from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QFormLayout,
                            QLineEdit, QPushButton, QLabel, QMessageBox,
                            QTableWidgetItem, QComboBox, QTextEdit)
from PyQt5.QtCore import Qt
from models import OperatorDAO, SuppDAO, GoodsDAO, SortDAO, UnitDAO, AeroDAO, DeptDAO
from .base_table_widget import BaseTableWidget
from utils import logger

class OperatorDialog(QDialog):
    """操作员编辑对话框"""
    
    def __init__(self, parent=None, title="编辑操作员", data=None):
        super().__init__(parent)
        self.data = data
        self.dept_dao = DeptDAO()
        self.init_ui(title)
        self.load_depts()
        if data:
            self.load_data()
            
    def init_ui(self, title):
        """初始化UI"""
        self.setWindowTitle(title)
        self.setModal(True)
        self.setFixedSize(450, 300)
        
        layout = QVBoxLayout()
        
        # 表单布局
        form_layout = QFormLayout()
        
        self.code_edit = QLineEdit()
        self.name_edit = QLineEdit()
        self.password_edit = QLineEdit()
        self.password_edit.setEchoMode(QLineEdit.Password)
        self.dept_combo = QComboBox()
        self.power_edit = QLineEdit()
        
        form_layout.addRow("操作员代码:", self.code_edit)
        form_layout.addRow("操作员姓名:", self.name_edit)
        form_layout.addRow("密码:", self.password_edit)
        form_layout.addRow("所属部门:", self.dept_combo)
        form_layout.addRow("权限:", self.power_edit)
        
        layout.addLayout(form_layout)
        
        # 按钮布局
        button_layout = QHBoxLayout()
        
        self.ok_btn = QPushButton("确定")
        self.ok_btn.clicked.connect(self.accept)
        button_layout.addWidget(self.ok_btn)
        
        self.cancel_btn = QPushButton("取消")
        self.cancel_btn.clicked.connect(self.reject)
        button_layout.addWidget(self.cancel_btn)
        
        layout.addLayout(button_layout)
        
        self.setLayout(layout)
        
        # 如果是编辑模式，代码不可修改
        if self.data:
            self.code_edit.setReadOnly(True)
            
    def load_depts(self):
        """加载部门列表"""
        try:
            depts = self.dept_dao.get_all_depts()
            self.dept_combo.clear()
            self.dept_combo.addItem("", "")  # 空选项
            for dept in depts:
                self.dept_combo.addItem(dept['dept_name'], dept['dept_code'])
        except Exception as e:
            logger.error(f"加载部门列表失败: {e}")
            
    def load_data(self):
        """加载数据"""
        if self.data:
            self.code_edit.setText(self.data.get('oper_code', ''))
            self.name_edit.setText(self.data.get('oper_name', ''))
            self.password_edit.setText(self.data.get('password', ''))
            self.power_edit.setText(self.data.get('power', ''))
            
            # 设置部门
            dept_code = self.data.get('dept_code', '')
            for i in range(self.dept_combo.count()):
                if self.dept_combo.itemData(i) == dept_code:
                    self.dept_combo.setCurrentIndex(i)
                    break
                    
    def get_data(self):
        """获取数据"""
        return {
            'oper_code': self.code_edit.text().strip(),
            'oper_name': self.name_edit.text().strip(),
            'password': self.password_edit.text().strip(),
            'dept_code': self.dept_combo.currentData(),
            'power': self.power_edit.text().strip()
        }
        
    def validate_data(self):
        """验证数据"""
        data = self.get_data()
        if not data['oper_code']:
            QMessageBox.warning(self, "提示", "请输入操作员代码！")
            return False
        if not data['oper_name']:
            QMessageBox.warning(self, "提示", "请输入操作员姓名！")
            return False
        if not data['password']:
            QMessageBox.warning(self, "提示", "请输入密码！")
            return False
        return True
        
    def accept(self):
        """确定按钮事件"""
        if self.validate_data():
            super().accept()

class SuppDialog(QDialog):
    """往来单位编辑对话框"""

    def __init__(self, parent=None, title="编辑往来单位", data=None):
        super().__init__(parent)
        self.data = data
        self.init_ui(title)
        if data:
            self.load_data()

    def init_ui(self, title):
        """初始化UI"""
        self.setWindowTitle(title)
        self.setModal(True)
        self.setFixedSize(500, 400)

        layout = QVBoxLayout()

        # 表单布局
        form_layout = QFormLayout()

        self.code_edit = QLineEdit()
        self.name_edit = QLineEdit()
        self.zip_edit = QLineEdit()
        self.addr_edit = QLineEdit()
        self.tel_edit = QLineEdit()
        self.fax_edit = QLineEdit()
        self.email_edit = QLineEdit()
        self.web_edit = QLineEdit()
        self.account_edit = QLineEdit()
        self.bank_edit = QLineEdit()

        form_layout.addRow("单位代码:", self.code_edit)
        form_layout.addRow("单位名称:", self.name_edit)
        form_layout.addRow("邮编:", self.zip_edit)
        form_layout.addRow("地址:", self.addr_edit)
        form_layout.addRow("电话:", self.tel_edit)
        form_layout.addRow("传真:", self.fax_edit)
        form_layout.addRow("邮箱:", self.email_edit)
        form_layout.addRow("网址:", self.web_edit)
        form_layout.addRow("银行账号:", self.account_edit)
        form_layout.addRow("开户银行:", self.bank_edit)

        layout.addLayout(form_layout)

        # 按钮布局
        button_layout = QHBoxLayout()

        self.ok_btn = QPushButton("确定")
        self.ok_btn.clicked.connect(self.accept)
        button_layout.addWidget(self.ok_btn)

        self.cancel_btn = QPushButton("取消")
        self.cancel_btn.clicked.connect(self.reject)
        button_layout.addWidget(self.cancel_btn)

        layout.addLayout(button_layout)

        self.setLayout(layout)

        # 如果是编辑模式，代码不可修改
        if self.data:
            self.code_edit.setReadOnly(True)

    def load_data(self):
        """加载数据"""
        if self.data:
            self.code_edit.setText(self.data.get('supp_code', ''))
            self.name_edit.setText(self.data.get('supp_name', ''))
            self.zip_edit.setText(self.data.get('zip', ''))
            self.addr_edit.setText(self.data.get('addr', ''))
            self.tel_edit.setText(self.data.get('tel', ''))
            self.fax_edit.setText(self.data.get('fax', ''))
            self.email_edit.setText(self.data.get('email', ''))
            self.web_edit.setText(self.data.get('web', ''))
            self.account_edit.setText(self.data.get('account', ''))
            self.bank_edit.setText(self.data.get('bank', ''))

    def get_data(self):
        """获取数据"""
        return {
            'supp_code': self.code_edit.text().strip(),
            'supp_name': self.name_edit.text().strip(),
            'zip': self.zip_edit.text().strip(),
            'addr': self.addr_edit.text().strip(),
            'tel': self.tel_edit.text().strip(),
            'fax': self.fax_edit.text().strip(),
            'email': self.email_edit.text().strip(),
            'web': self.web_edit.text().strip(),
            'account': self.account_edit.text().strip(),
            'bank': self.bank_edit.text().strip()
        }

    def validate_data(self):
        """验证数据"""
        data = self.get_data()
        if not data['supp_code']:
            QMessageBox.warning(self, "提示", "请输入单位代码！")
            return False
        if not data['supp_name']:
            QMessageBox.warning(self, "提示", "请输入单位名称！")
            return False
        return True

    def accept(self):
        """确定按钮事件"""
        if self.validate_data():
            super().accept()

class GoodsDialog(QDialog):
    """商品信息编辑对话框"""

    def __init__(self, parent=None, title="编辑商品信息", data=None):
        super().__init__(parent)
        self.data = data
        self.sort_dao = SortDAO()
        self.unit_dao = UnitDAO()
        self.aero_dao = AeroDAO()
        self.supp_dao = SuppDAO()
        self.init_ui(title)
        self.load_combo_data()
        if data:
            self.load_data()

    def init_ui(self, title):
        """初始化UI"""
        self.setWindowTitle(title)
        self.setModal(True)
        self.setFixedSize(500, 450)

        layout = QVBoxLayout()

        # 表单布局
        form_layout = QFormLayout()

        self.code_edit = QLineEdit()
        self.name_edit = QLineEdit()
        self.sort_combo = QComboBox()
        self.model_edit = QLineEdit()
        self.unit_combo = QComboBox()
        self.price_retail_edit = QLineEdit()
        self.price_plan_edit = QLineEdit()
        self.aero_combo = QComboBox()
        self.supp_combo = QComboBox()
        self.note_edit = QTextEdit()
        self.note_edit.setMaximumHeight(80)

        form_layout.addRow("商品代码:", self.code_edit)
        form_layout.addRow("商品名称:", self.name_edit)
        form_layout.addRow("商品种类:", self.sort_combo)
        form_layout.addRow("规格型号:", self.model_edit)
        form_layout.addRow("计量单位:", self.unit_combo)
        form_layout.addRow("零售价:", self.price_retail_edit)
        form_layout.addRow("计划价:", self.price_plan_edit)
        form_layout.addRow("产地:", self.aero_combo)
        form_layout.addRow("供应商:", self.supp_combo)
        form_layout.addRow("备注:", self.note_edit)

        layout.addLayout(form_layout)

        # 按钮布局
        button_layout = QHBoxLayout()

        self.ok_btn = QPushButton("确定")
        self.ok_btn.clicked.connect(self.accept)
        button_layout.addWidget(self.ok_btn)

        self.cancel_btn = QPushButton("取消")
        self.cancel_btn.clicked.connect(self.reject)
        button_layout.addWidget(self.cancel_btn)

        layout.addLayout(button_layout)

        self.setLayout(layout)

        # 如果是编辑模式，代码不可修改
        if self.data:
            self.code_edit.setReadOnly(True)

    def load_combo_data(self):
        """加载下拉框数据"""
        try:
            # 加载商品种类
            sorts = self.sort_dao.get_all_sorts()
            self.sort_combo.clear()
            self.sort_combo.addItem("", "")
            for sort in sorts:
                self.sort_combo.addItem(sort['sort_name'], sort['sort_code'])

            # 加载计量单位
            units = self.unit_dao.get_all_units()
            self.unit_combo.clear()
            self.unit_combo.addItem("", "")
            for unit in units:
                self.unit_combo.addItem(unit['unit_name'], unit['unit_code'])

            # 加载产地
            aeros = self.aero_dao.get_all_aeros()
            self.aero_combo.clear()
            self.aero_combo.addItem("", "")
            for aero in aeros:
                self.aero_combo.addItem(aero['aero_name'], aero['aero_code'])

            # 加载供应商
            supps = self.supp_dao.get_all_supps()
            self.supp_combo.clear()
            self.supp_combo.addItem("", "")
            for supp in supps:
                self.supp_combo.addItem(supp['supp_name'], supp['supp_code'])

        except Exception as e:
            logger.error(f"加载下拉框数据失败: {e}")

    def load_data(self):
        """加载数据"""
        if self.data:
            self.code_edit.setText(self.data.get('goods_code', ''))
            self.name_edit.setText(self.data.get('goods_name', ''))
            self.model_edit.setText(self.data.get('model', ''))
            self.price_retail_edit.setText(str(self.data.get('price_retail', '') if self.data.get('price_retail') else ''))
            self.price_plan_edit.setText(str(self.data.get('price_plan', '') if self.data.get('price_plan') else ''))
            self.note_edit.setPlainText(self.data.get('note', ''))

            # 设置下拉框选中项
            self.set_combo_value(self.sort_combo, self.data.get('sort_code', ''))
            self.set_combo_value(self.unit_combo, self.data.get('unit_code', ''))
            self.set_combo_value(self.aero_combo, self.data.get('aero_code', ''))
            self.set_combo_value(self.supp_combo, self.data.get('supp_code', ''))

    def set_combo_value(self, combo, value):
        """设置下拉框值"""
        for i in range(combo.count()):
            if combo.itemData(i) == value:
                combo.setCurrentIndex(i)
                break

    def get_data(self):
        """获取数据"""
        return {
            'goods_code': self.code_edit.text().strip(),
            'goods_name': self.name_edit.text().strip(),
            'sort_code': self.sort_combo.currentData(),
            'model': self.model_edit.text().strip(),
            'unit_code': self.unit_combo.currentData(),
            'price_retail': float(self.price_retail_edit.text()) if self.price_retail_edit.text() else None,
            'price_plan': float(self.price_plan_edit.text()) if self.price_plan_edit.text() else None,
            'aero_code': self.aero_combo.currentData(),
            'supp_code': self.supp_combo.currentData(),
            'note': self.note_edit.toPlainText().strip()
        }

    def validate_data(self):
        """验证数据"""
        data = self.get_data()
        if not data['goods_code']:
            QMessageBox.warning(self, "提示", "请输入商品代码！")
            return False
        if not data['goods_name']:
            QMessageBox.warning(self, "提示", "请输入商品名称！")
            return False

        # 验证价格格式
        try:
            if self.price_retail_edit.text():
                float(self.price_retail_edit.text())
            if self.price_plan_edit.text():
                float(self.price_plan_edit.text())
        except ValueError:
            QMessageBox.warning(self, "提示", "价格格式不正确！")
            return False

        return True

    def accept(self):
        """确定按钮事件"""
        if self.validate_data():
            super().accept()

class OperatorManagementWindow(BaseTableWidget):
    """操作员管理窗口"""
    
    def __init__(self):
        super().__init__("操作员管理")
        self.dao = OperatorDAO()
        self.setup_table()
        self.load_data()
        
    def setup_table(self):
        """设置表格"""
        self.table.setColumnCount(5)
        self.table.setHorizontalHeaderLabels(["操作员代码", "操作员姓名", "所属部门", "权限", "密码"])
        
        # 设置列宽
        header = self.table.horizontalHeader()
        header.resizeSection(0, 100)
        header.resizeSection(1, 120)
        header.resizeSection(2, 100)
        header.resizeSection(3, 150)
        header.resizeSection(4, 80)
        
    def load_data(self):
        """加载数据"""
        try:
            data = self.dao.get_all_operators()
            self.table.setRowCount(len(data))
            
            for row, item in enumerate(data):
                self.table.setItem(row, 0, QTableWidgetItem(str(item['oper_code'])))
                self.table.setItem(row, 1, QTableWidgetItem(str(item['oper_name'] or '')))
                self.table.setItem(row, 2, QTableWidgetItem(str(item['dept_name'] or '')))
                self.table.setItem(row, 3, QTableWidgetItem(str(item['power'] or '')))
                self.table.setItem(row, 4, QTableWidgetItem("******"))  # 隐藏密码
                
        except Exception as e:
            logger.error(f"加载操作员数据失败: {e}")
            self.show_message("错误", f"加载数据失败: {str(e)}", "error")
            
    def search_data(self):
        """搜索数据"""
        keyword = self.search_edit.text().strip()
        if not keyword:
            self.load_data()
            return
            
        try:
            for row in range(self.table.rowCount()):
                code_item = self.table.item(row, 0)
                name_item = self.table.item(row, 1)
                dept_item = self.table.item(row, 2)
                
                code_text = code_item.text() if code_item else ""
                name_text = name_item.text() if name_item else ""
                dept_text = dept_item.text() if dept_item else ""
                
                if (keyword.lower() in code_text.lower() or 
                    keyword.lower() in name_text.lower() or 
                    keyword.lower() in dept_text.lower()):
                    self.table.setRowHidden(row, False)
                else:
                    self.table.setRowHidden(row, True)
                    
        except Exception as e:
            logger.error(f"搜索操作员失败: {e}")
            
    def add_item(self):
        """添加操作员"""
        dialog = OperatorDialog(self, "添加操作员")
        if dialog.exec_() == dialog.Accepted:
            data = dialog.get_data()
            try:
                if self.dao.operator_exists(data['oper_code']):
                    self.show_message("提示", "操作员代码已存在！", "warning")
                    return
                    
                if self.dao.add_operator(data['oper_code'], data['oper_name'], 
                                       data['password'], data['dept_code'], data['power']):
                    self.show_message("成功", "添加成功！")
                    self.load_data()
                else:
                    self.show_message("错误", "添加失败！", "error")
                    
            except Exception as e:
                logger.error(f"添加操作员失败: {e}")
                self.show_message("错误", f"添加失败: {str(e)}", "error")
                
    def edit_item(self):
        """编辑操作员"""
        if self.current_row < 0:
            return
            
        data = self.get_selected_data()
        if not data:
            return
            
        dialog = OperatorDialog(self, "修改操作员", data)
        if dialog.exec_() == dialog.Accepted:
            new_data = dialog.get_data()
            try:
                if self.dao.update_operator(new_data['oper_code'], new_data['oper_name'],
                                          new_data['password'], new_data['dept_code'], 
                                          new_data['power']):
                    self.show_message("成功", "修改成功！")
                    self.load_data()
                else:
                    self.show_message("错误", "修改失败！", "error")
                    
            except Exception as e:
                logger.error(f"修改操作员失败: {e}")
                self.show_message("错误", f"修改失败: {str(e)}", "error")
                
    def delete_item(self):
        """删除操作员"""
        if self.current_row < 0:
            return
            
        data = self.get_selected_data()
        if not data:
            return
            
        if self.confirm_delete(data['oper_name']):
            try:
                if self.dao.delete_operator(data['oper_code']):
                    self.show_message("成功", "删除成功！")
                    self.load_data()
                else:
                    self.show_message("错误", "删除失败！", "error")
                    
            except Exception as e:
                logger.error(f"删除操作员失败: {e}")
                self.show_message("错误", f"删除失败: {str(e)}", "error")
                
    def get_selected_data(self):
        """获取选中行的数据"""
        if self.current_row < 0:
            return None
            
        # 需要从数据库重新获取完整数据
        code_item = self.table.item(self.current_row, 0)
        if not code_item:
            return None
            
        try:
            return self.dao.get_operator_by_code(code_item.text())
        except Exception as e:
            logger.error(f"获取操作员数据失败: {e}")
            return None

class SuppManagementWindow(BaseTableWidget):
    """往来单位管理窗口"""

    def __init__(self):
        super().__init__("往来单位管理")
        self.dao = SuppDAO()
        self.setup_table()
        self.load_data()

    def setup_table(self):
        """设置表格"""
        self.table.setColumnCount(6)
        self.table.setHorizontalHeaderLabels(["单位代码", "单位名称", "地址", "电话", "邮箱", "银行"])

        # 设置列宽
        header = self.table.horizontalHeader()
        header.resizeSection(0, 100)
        header.resizeSection(1, 150)
        header.resizeSection(2, 200)
        header.resizeSection(3, 120)
        header.resizeSection(4, 150)
        header.resizeSection(5, 150)

    def load_data(self):
        """加载数据"""
        try:
            data = self.dao.get_all_supps()
            self.table.setRowCount(len(data))

            for row, item in enumerate(data):
                self.table.setItem(row, 0, QTableWidgetItem(str(item['supp_code'])))
                self.table.setItem(row, 1, QTableWidgetItem(str(item['supp_name'] or '')))
                self.table.setItem(row, 2, QTableWidgetItem(str(item['addr'] or '')))
                self.table.setItem(row, 3, QTableWidgetItem(str(item['tel'] or '')))
                self.table.setItem(row, 4, QTableWidgetItem(str(item['email'] or '')))
                self.table.setItem(row, 5, QTableWidgetItem(str(item['bank'] or '')))

        except Exception as e:
            logger.error(f"加载往来单位数据失败: {e}")
            self.show_message("错误", f"加载数据失败: {str(e)}", "error")

    def search_data(self):
        """搜索数据"""
        keyword = self.search_edit.text().strip()
        if not keyword:
            self.load_data()
            return

        try:
            for row in range(self.table.rowCount()):
                code_item = self.table.item(row, 0)
                name_item = self.table.item(row, 1)

                code_text = code_item.text() if code_item else ""
                name_text = name_item.text() if name_item else ""

                if keyword.lower() in code_text.lower() or keyword.lower() in name_text.lower():
                    self.table.setRowHidden(row, False)
                else:
                    self.table.setRowHidden(row, True)

        except Exception as e:
            logger.error(f"搜索往来单位失败: {e}")

    def add_item(self):
        """添加往来单位"""
        dialog = SuppDialog(self, "添加往来单位")
        if dialog.exec_() == dialog.Accepted:
            data = dialog.get_data()
            try:
                if self.dao.supp_exists(data['supp_code']):
                    self.show_message("提示", "单位代码已存在！", "warning")
                    return

                if self.dao.add_supp(data['supp_code'], data['supp_name'],
                                   data['zip'], data['addr'], data['tel'],
                                   data['fax'], data['email'], data['web'],
                                   data['account'], data['bank']):
                    self.show_message("成功", "添加成功！")
                    self.load_data()
                else:
                    self.show_message("错误", "添加失败！", "error")

            except Exception as e:
                logger.error(f"添加往来单位失败: {e}")
                self.show_message("错误", f"添加失败: {str(e)}", "error")

    def edit_item(self):
        """编辑往来单位"""
        if self.current_row < 0:
            return

        data = self.get_selected_data()
        if not data:
            return

        dialog = SuppDialog(self, "修改往来单位", data)
        if dialog.exec_() == dialog.Accepted:
            new_data = dialog.get_data()
            try:
                if self.dao.update_supp(new_data['supp_code'], new_data['supp_name'],
                                      new_data['zip'], new_data['addr'], new_data['tel'],
                                      new_data['fax'], new_data['email'], new_data['web'],
                                      new_data['account'], new_data['bank']):
                    self.show_message("成功", "修改成功！")
                    self.load_data()
                else:
                    self.show_message("错误", "修改失败！", "error")

            except Exception as e:
                logger.error(f"修改往来单位失败: {e}")
                self.show_message("错误", f"修改失败: {str(e)}", "error")

    def delete_item(self):
        """删除往来单位"""
        if self.current_row < 0:
            return

        data = self.get_selected_data()
        if not data:
            return

        if self.confirm_delete(data['supp_name']):
            try:
                if self.dao.delete_supp(data['supp_code']):
                    self.show_message("成功", "删除成功！")
                    self.load_data()
                else:
                    self.show_message("错误", "删除失败！", "error")

            except Exception as e:
                logger.error(f"删除往来单位失败: {e}")
                self.show_message("错误", f"删除失败: {str(e)}", "error")

    def get_selected_data(self):
        """获取选中行的数据"""
        if self.current_row < 0:
            return None

        code_item = self.table.item(self.current_row, 0)
        if not code_item:
            return None

        try:
            return self.dao.get_supp_by_code(code_item.text())
        except Exception as e:
            logger.error(f"获取往来单位数据失败: {e}")
            return None

class GoodsManagementWindow(BaseTableWidget):
    """商品管理窗口"""

    def __init__(self):
        super().__init__("商品管理")
        self.dao = GoodsDAO()
        self.setup_table()
        self.load_data()

    def setup_table(self):
        """设置表格"""
        self.table.setColumnCount(8)
        self.table.setHorizontalHeaderLabels(["商品代码", "商品名称", "种类", "规格型号", "单位", "零售价", "产地", "供应商"])

        # 设置列宽
        header = self.table.horizontalHeader()
        header.resizeSection(0, 100)
        header.resizeSection(1, 150)
        header.resizeSection(2, 80)
        header.resizeSection(3, 120)
        header.resizeSection(4, 60)
        header.resizeSection(5, 80)
        header.resizeSection(6, 100)
        header.resizeSection(7, 120)

    def load_data(self):
        """加载数据"""
        try:
            data = self.dao.get_all_goods()
            self.table.setRowCount(len(data))

            for row, item in enumerate(data):
                self.table.setItem(row, 0, QTableWidgetItem(str(item['goods_code'])))
                self.table.setItem(row, 1, QTableWidgetItem(str(item['goods_name'] or '')))
                self.table.setItem(row, 2, QTableWidgetItem(str(item['sort_name'] or '')))
                self.table.setItem(row, 3, QTableWidgetItem(str(item['model'] or '')))
                self.table.setItem(row, 4, QTableWidgetItem(str(item['unit_name'] or '')))
                self.table.setItem(row, 5, QTableWidgetItem(str(item['price_retail'] or '')))
                self.table.setItem(row, 6, QTableWidgetItem(str(item['aero_name'] or '')))
                self.table.setItem(row, 7, QTableWidgetItem(str(item['supp_name'] or '')))

        except Exception as e:
            logger.error(f"加载商品数据失败: {e}")
            self.show_message("错误", f"加载数据失败: {str(e)}", "error")

    def search_data(self):
        """搜索数据"""
        keyword = self.search_edit.text().strip()
        if not keyword:
            self.load_data()
            return

        try:
            for row in range(self.table.rowCount()):
                code_item = self.table.item(row, 0)
                name_item = self.table.item(row, 1)

                code_text = code_item.text() if code_item else ""
                name_text = name_item.text() if name_item else ""

                if keyword.lower() in code_text.lower() or keyword.lower() in name_text.lower():
                    self.table.setRowHidden(row, False)
                else:
                    self.table.setRowHidden(row, True)

        except Exception as e:
            logger.error(f"搜索商品失败: {e}")

    def add_item(self):
        """添加商品"""
        dialog = GoodsDialog(self, "添加商品")
        if dialog.exec_() == dialog.Accepted:
            data = dialog.get_data()
            try:
                if self.dao.goods_exists(data['goods_code']):
                    self.show_message("提示", "商品代码已存在！", "warning")
                    return

                if self.dao.add_goods(data['goods_code'], data['goods_name'],
                                    data['sort_code'], data['model'], data['unit_code'],
                                    data['price_retail'], data['price_plan'],
                                    data['aero_code'], data['supp_code'], data['note']):
                    self.show_message("成功", "添加成功！")
                    self.load_data()
                else:
                    self.show_message("错误", "添加失败！", "error")

            except Exception as e:
                logger.error(f"添加商品失败: {e}")
                self.show_message("错误", f"添加失败: {str(e)}", "error")

    def edit_item(self):
        """编辑商品"""
        if self.current_row < 0:
            return

        data = self.get_selected_data()
        if not data:
            return

        dialog = GoodsDialog(self, "修改商品", data)
        if dialog.exec_() == dialog.Accepted:
            new_data = dialog.get_data()
            try:
                if self.dao.update_goods(new_data['goods_code'], new_data['goods_name'],
                                       new_data['sort_code'], new_data['model'],
                                       new_data['unit_code'], new_data['price_retail'],
                                       new_data['price_plan'], new_data['aero_code'],
                                       new_data['supp_code'], new_data['note']):
                    self.show_message("成功", "修改成功！")
                    self.load_data()
                else:
                    self.show_message("错误", "修改失败！", "error")

            except Exception as e:
                logger.error(f"修改商品失败: {e}")
                self.show_message("错误", f"修改失败: {str(e)}", "error")

    def delete_item(self):
        """删除商品"""
        if self.current_row < 0:
            return

        data = self.get_selected_data()
        if not data:
            return

        if self.confirm_delete(data['goods_name']):
            try:
                if self.dao.delete_goods(data['goods_code']):
                    self.show_message("成功", "删除成功！")
                    self.load_data()
                else:
                    self.show_message("错误", "删除失败！", "error")

            except Exception as e:
                logger.error(f"删除商品失败: {e}")
                self.show_message("错误", f"删除失败: {str(e)}", "error")

    def get_selected_data(self):
        """获取选中行的数据"""
        if self.current_row < 0:
            return None

        code_item = self.table.item(self.current_row, 0)
        if not code_item:
            return None

        try:
            return self.dao.get_goods_by_code(code_item.text())
        except Exception as e:
            logger.error(f"获取商品数据失败: {e}")
            return None
