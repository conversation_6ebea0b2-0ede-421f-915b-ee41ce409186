# JX进销存系统 - 业务单据管理功能演示

## 功能概述

本次开发完成了JX进销存系统的核心业务单据管理功能，包括采购单管理和销售单管理。这些功能实现了完整的业务流程管理，支持单据的录入、编辑、删除、查询和确认操作。

## 新增功能

### 1. 采购单管理

#### 功能特点
- **完整的单据管理流程**：支持采购单的新增、编辑、删除、查询操作
- **主从表联动**：采购单主表与明细表数据联动管理
- **自动单据号生成**：按日期格式自动生成单据号（如：CG20250720001）
- **数据验证**：完善的数据验证和错误处理机制
- **单据确认功能**：支持单据确认，确认后不可修改
- **搜索功能**：支持按单据号、供应商、操作员等关键字搜索

#### 操作流程
1. **新增采购单**
   - 点击"添加"按钮打开采购单编辑对话框
   - 填写单据日期、选择操作员和供应商
   - 添加采购明细：选择商品、输入数量和单价
   - 系统自动计算金额，支持税额录入
   - 点击确定保存采购单

2. **编辑采购单**
   - 在列表中选择要编辑的采购单
   - 点击"修改"按钮或双击记录
   - 修改主表信息或明细信息
   - 保存修改（仅未确认的单据可修改）

3. **确认采购单**
   - 选择要确认的采购单
   - 点击"确认单据"按钮
   - 确认后单据状态变为"已确认"，不可再修改

#### 界面特色
- **现代化设计**：采用分割器布局，主表信息和明细表分离显示
- **智能填充**：选择商品后自动填充计划价格
- **实时计算**：数量和单价变化时自动计算金额
- **用户友好**：提供丰富的提示信息和错误处理

### 2. 销售单管理

#### 功能特点
- **完整的销售流程**：支持销售单的新增、编辑、删除、查询操作
- **折扣支持**：销售明细支持折扣设置，自动计算折后金额
- **价格管理**：自动获取商品零售价作为默认销售价格
- **单据确认**：支持销售单确认，确认后锁定数据
- **搜索查询**：支持多条件搜索和数据筛选

#### 操作流程
1. **新增销售单**
   - 点击"添加"按钮打开销售单编辑对话框
   - 填写单据日期、选择操作员
   - 添加销售明细：选择商品、输入数量、单价和折扣
   - 系统自动计算折后金额
   - 保存销售单

2. **编辑销售单**
   - 选择要编辑的销售单
   - 点击"修改"按钮进入编辑模式
   - 修改相关信息并保存

3. **确认销售单**
   - 选择要确认的销售单
   - 点击"确认单据"按钮完成确认

#### 界面特色
- **折扣计算**：支持0.001-1.000范围的折扣设置
- **价格联动**：选择商品后自动填充零售价格
- **实时更新**：金额随数量、单价、折扣变化实时更新

## 技术实现

### 1. 数据访问层（DAO）

#### PurchaseDAO（采购单DAO）
- **单据号生成**：`generate_sheet_id()` - 按日期格式生成唯一单据号
- **分页查询**：`get_all_purchases()` - 支持分页和搜索的数据查询
- **完整数据获取**：`get_purchase_by_id()` - 获取单据主表信息
- **明细数据获取**：`get_purchase_items()` - 获取单据明细信息
- **事务处理**：`add_purchase()`, `update_purchase()` - 主从表事务操作
- **单据确认**：`confirm_purchase()` - 单据确认状态管理

#### SalesDAO（销售单DAO）
- 与采购单DAO类似的完整功能
- 支持销售单特有的折扣字段处理
- 自动生成XS开头的销售单号

### 2. 用户界面层（UI）

#### 对话框设计
- **主从分离**：使用QSplitter实现主表和明细表的分离显示
- **表格操作**：明细表格支持增删改操作
- **数据验证**：完善的输入验证和错误提示
- **智能交互**：商品选择联动价格填充

#### 管理窗口
- **继承BaseTableWidget**：复用基础表格管理功能
- **自定义按钮**：添加"确认单据"等业务特定按钮
- **状态显示**：直观显示单据确认状态

### 3. 数据库设计

#### 表结构
- **jx_sheet_cg_main**：采购单主表
- **jx_sheet_cg_item**：采购单明细表
- **jx_sheet_xs_main**：销售单主表
- **jx_sheet_xs_item**：销售单明细表

#### 关键字段
- **zth**：账套号（支持多账套）
- **sheetid**：单据号（主键）
- **flag_qr**：确认标志（0-未确认，1-已确认）
- **itemno**：明细行号（明细表主键之一）

## 使用说明

### 1. 系统启动
1. 运行 `python main.py`
2. 使用默认账号登录（admin/123456）
3. 系统主界面加载完成

### 2. 采购管理
1. 点击菜单"业务处理" → "采购管理"
2. 在采购单管理窗口进行相关操作
3. 使用搜索功能快速定位单据

### 3. 销售管理
1. 点击菜单"业务处理" → "销售管理"
2. 在销售单管理窗口进行相关操作
3. 注意销售单支持折扣功能

### 4. 注意事项
- 确认后的单据不能修改或删除
- 单据号系统自动生成，不需手工输入
- 明细表至少需要一行数据才能保存
- 商品、操作员、供应商等基础数据需要预先维护

## 后续开发计划

### 即将开发的功能
1. **报损单管理**：处理商品报损业务
2. **盘存单管理**：库存盘点功能
3. **收付款单管理**：资金收付管理
4. **库存计算模块**：实时库存计算和报表
5. **成本计算模块**：多种成本计算方法

### 功能增强
1. **打印功能**：单据打印和预览
2. **导入导出**：Excel数据导入导出
3. **审批流程**：多级审批机制
4. **权限控制**：细粒度权限管理

## 总结

本次开发成功实现了JX进销存系统的核心业务单据管理功能，为后续的库存管理、成本计算和财务管理奠定了坚实基础。系统采用现代化的UI设计和完善的数据处理机制，提供了良好的用户体验和数据安全保障。

通过采购单和销售单的管理，用户可以完整地记录和跟踪商品的进销存业务流程，为企业的经营决策提供准确的数据支持。
