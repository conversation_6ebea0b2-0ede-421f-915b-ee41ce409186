# -*- coding: utf-8 -*-
"""
业务单据数据访问对象
"""

from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime, date
from .base_dao import BaseDAO
from utils import logger


class PurchaseDAO(BaseDAO):
    """采购单数据访问对象"""
    
    def __init__(self):
        super().__init__()
        self.main_table = 'jx_sheet_cg_main'
        self.item_table = 'jx_sheet_cg_item'
        self.zth = '01'  # 默认账套号
    
    def generate_sheet_id(self) -> str:
        """生成单据号"""
        today = datetime.now()
        prefix = f"CG{today.strftime('%Y%m%d')}"
        
        # 查询当天最大单据号
        sql = """
        SELECT MAX(sheetid) as max_id 
        FROM jx_sheet_cg_main 
        WHERE sheetid LIKE %s
        """
        result = self.execute_query(sql, (f"{prefix}%",))
        
        if result and result[0]['max_id']:
            # 提取序号并加1
            last_id = result[0]['max_id']
            seq = int(last_id[-3:]) + 1
        else:
            seq = 1
        
        return f"{prefix}{seq:03d}"
    
    def get_all_purchases(self, page: int = 1, page_size: int = 50, 
                         search_text: str = None) -> Tuple[List[Dict[str, Any]], int]:
        """获取所有采购单（分页）"""
        where_clause = "WHERE m.zth = %s"
        params = [self.zth]
        
        if search_text:
            where_clause += " AND (m.sheetid LIKE %s OR s.supp_name LIKE %s OR o.oper_name LIKE %s)"
            search_param = f"%{search_text}%"
            params.extend([search_param, search_param, search_param])
        
        # 查询总数
        count_sql = f"""
        SELECT COUNT(*) as total
        FROM jx_sheet_cg_main m
        LEFT JOIN jx_d_supp s ON m.supp_code = s.supp_code
        LEFT JOIN jx_d_operator o ON m.oper_code = o.oper_code
        {where_clause}
        """
        count_result = self.execute_query(count_sql, params)
        total = count_result[0]['total'] if count_result else 0
        
        # 查询数据
        offset = (page - 1) * page_size
        sql = f"""
        SELECT m.*, s.supp_name, o.oper_name, oq.oper_name as oper_qr_name
        FROM jx_sheet_cg_main m
        LEFT JOIN jx_d_supp s ON m.supp_code = s.supp_code
        LEFT JOIN jx_d_operator o ON m.oper_code = o.oper_code
        LEFT JOIN jx_d_operator oq ON m.oper_qr = oq.oper_code
        {where_clause}
        ORDER BY m.sheetdate DESC, m.sheetid DESC
        LIMIT %s OFFSET %s
        """
        params.extend([page_size, offset])
        data = self.execute_query(sql, params)
        
        return data, total
    
    def get_purchase_by_id(self, sheet_id: str) -> Optional[Dict[str, Any]]:
        """根据单据号获取采购单主表信息"""
        sql = """
        SELECT m.*, s.supp_name, o.oper_name, oq.oper_name as oper_qr_name
        FROM jx_sheet_cg_main m
        LEFT JOIN jx_d_supp s ON m.supp_code = s.supp_code
        LEFT JOIN jx_d_operator o ON m.oper_code = o.oper_code
        LEFT JOIN jx_d_operator oq ON m.oper_qr = oq.oper_code
        WHERE m.zth = %s AND m.sheetid = %s
        """
        result = self.execute_query(sql, (self.zth, sheet_id))
        return result[0] if result else None
    
    def get_purchase_items(self, sheet_id: str) -> List[Dict[str, Any]]:
        """获取采购单明细"""
        sql = """
        SELECT i.*, g.goods_name, u.unit_name
        FROM jx_sheet_cg_item i
        LEFT JOIN jx_d_goods g ON i.goods_code = g.goods_code
        LEFT JOIN jx_c_unit u ON g.unit_code = u.unit_code
        WHERE i.zth = %s AND i.sheetid = %s
        ORDER BY i.itemno
        """
        return self.execute_query(sql, (self.zth, sheet_id))
    
    def add_purchase(self, main_data: Dict[str, Any], items_data: List[Dict[str, Any]]) -> bool:
        """添加采购单（主表+明细表）"""
        try:
            self.begin_transaction()
            
            # 生成单据号
            sheet_id = self.generate_sheet_id()
            
            # 插入主表
            main_data['zth'] = self.zth
            main_data['sheetid'] = sheet_id
            main_data['flag_qr'] = '0'  # 未确认
            
            if not self.insert(self.main_table, main_data):
                self.rollback_transaction()
                return False
            
            # 插入明细表
            for i, item in enumerate(items_data, 1):
                item['zth'] = self.zth
                item['sheetid'] = sheet_id
                item['itemno'] = i
                
                if not self.insert(self.item_table, item):
                    self.rollback_transaction()
                    return False
            
            self.commit_transaction()
            logger.info(f"添加采购单成功: {sheet_id}")
            return True
            
        except Exception as e:
            self.rollback_transaction()
            logger.error(f"添加采购单失败: {e}")
            return False
    
    def update_purchase(self, sheet_id: str, main_data: Dict[str, Any], 
                       items_data: List[Dict[str, Any]]) -> bool:
        """更新采购单"""
        try:
            self.begin_transaction()
            
            # 检查是否已确认
            purchase = self.get_purchase_by_id(sheet_id)
            if purchase and purchase.get('flag_qr') == '1':
                logger.warning(f"采购单 {sheet_id} 已确认，不能修改")
                self.rollback_transaction()
                return False
            
            # 更新主表
            if not self.update(self.main_table, main_data, 
                             'zth = %s AND sheetid = %s', (self.zth, sheet_id)):
                self.rollback_transaction()
                return False
            
            # 删除原明细
            if not self.delete(self.item_table, 'zth = %s AND sheetid = %s', (self.zth, sheet_id)):
                self.rollback_transaction()
                return False
            
            # 插入新明细
            for i, item in enumerate(items_data, 1):
                item['zth'] = self.zth
                item['sheetid'] = sheet_id
                item['itemno'] = i
                
                if not self.insert(self.item_table, item):
                    self.rollback_transaction()
                    return False
            
            self.commit_transaction()
            logger.info(f"更新采购单成功: {sheet_id}")
            return True
            
        except Exception as e:
            self.rollback_transaction()
            logger.error(f"更新采购单失败: {e}")
            return False
    
    def delete_purchase(self, sheet_id: str) -> bool:
        """删除采购单"""
        try:
            self.begin_transaction()
            
            # 检查是否已确认
            purchase = self.get_purchase_by_id(sheet_id)
            if purchase and purchase.get('flag_qr') == '1':
                logger.warning(f"采购单 {sheet_id} 已确认，不能删除")
                self.rollback_transaction()
                return False
            
            # 删除明细
            if not self.delete(self.item_table, 'zth = %s AND sheetid = %s', (self.zth, sheet_id)):
                self.rollback_transaction()
                return False
            
            # 删除主表
            if not self.delete(self.main_table, 'zth = %s AND sheetid = %s', (self.zth, sheet_id)):
                self.rollback_transaction()
                return False
            
            self.commit_transaction()
            logger.info(f"删除采购单成功: {sheet_id}")
            return True
            
        except Exception as e:
            self.rollback_transaction()
            logger.error(f"删除采购单失败: {e}")
            return False
    
    def confirm_purchase(self, sheet_id: str, oper_code: str) -> bool:
        """确认采购单"""
        try:
            data = {
                'flag_qr': '1',
                'oper_qr': oper_code,
                'date_qr': date.today()
            }
            
            result = self.update(self.main_table, data, 
                               'zth = %s AND sheetid = %s AND flag_qr = %s', 
                               (self.zth, sheet_id, '0'))
            
            if result:
                logger.info(f"确认采购单成功: {sheet_id}")
            else:
                logger.warning(f"确认采购单失败: {sheet_id}")
            
            return result
            
        except Exception as e:
            logger.error(f"确认采购单失败: {e}")
            return False


class SalesDAO(BaseDAO):
    """销售单数据访问对象"""
    
    def __init__(self):
        super().__init__()
        self.main_table = 'jx_sheet_xs_main'
        self.item_table = 'jx_sheet_xs_item'
        self.zth = '01'  # 默认账套号
    
    def generate_sheet_id(self) -> str:
        """生成单据号"""
        today = datetime.now()
        prefix = f"XS{today.strftime('%Y%m%d')}"
        
        # 查询当天最大单据号
        sql = """
        SELECT MAX(sheetid) as max_id 
        FROM jx_sheet_xs_main 
        WHERE sheetid LIKE %s
        """
        result = self.execute_query(sql, (f"{prefix}%",))
        
        if result and result[0]['max_id']:
            # 提取序号并加1
            last_id = result[0]['max_id']
            seq = int(last_id[-3:]) + 1
        else:
            seq = 1
        
        return f"{prefix}{seq:03d}"
    
    def get_all_sales(self, page: int = 1, page_size: int = 50, 
                     search_text: str = None) -> Tuple[List[Dict[str, Any]], int]:
        """获取所有销售单（分页）"""
        where_clause = "WHERE m.zth = %s"
        params = [self.zth]
        
        if search_text:
            where_clause += " AND (m.sheetid LIKE %s OR o.oper_name LIKE %s)"
            search_param = f"%{search_text}%"
            params.extend([search_param, search_param])
        
        # 查询总数
        count_sql = f"""
        SELECT COUNT(*) as total
        FROM jx_sheet_xs_main m
        LEFT JOIN jx_d_operator o ON m.oper_code = o.oper_code
        {where_clause}
        """
        count_result = self.execute_query(count_sql, params)
        total = count_result[0]['total'] if count_result else 0
        
        # 查询数据
        offset = (page - 1) * page_size
        sql = f"""
        SELECT m.*, o.oper_name, oq.oper_name as oper_qr_name
        FROM jx_sheet_xs_main m
        LEFT JOIN jx_d_operator o ON m.oper_code = o.oper_code
        LEFT JOIN jx_d_operator oq ON m.oper_qr = oq.oper_code
        {where_clause}
        ORDER BY m.sheetdate DESC, m.sheetid DESC
        LIMIT %s OFFSET %s
        """
        params.extend([page_size, offset])
        data = self.execute_query(sql, params)
        
        return data, total

    def get_sales_by_id(self, sheet_id: str) -> Optional[Dict[str, Any]]:
        """根据单据号获取销售单主表信息"""
        sql = """
        SELECT m.*, o.oper_name, oq.oper_name as oper_qr_name
        FROM jx_sheet_xs_main m
        LEFT JOIN jx_d_operator o ON m.oper_code = o.oper_code
        LEFT JOIN jx_d_operator oq ON m.oper_qr = oq.oper_code
        WHERE m.zth = %s AND m.sheetid = %s
        """
        result = self.execute_query(sql, (self.zth, sheet_id))
        return result[0] if result else None

    def get_sales_items(self, sheet_id: str) -> List[Dict[str, Any]]:
        """获取销售单明细"""
        sql = """
        SELECT i.*, g.goods_name, u.unit_name
        FROM jx_sheet_xs_item i
        LEFT JOIN jx_d_goods g ON i.goods_code = g.goods_code
        LEFT JOIN jx_c_unit u ON g.unit_code = u.unit_code
        WHERE i.zth = %s AND i.sheetid = %s
        ORDER BY i.itemno
        """
        return self.execute_query(sql, (self.zth, sheet_id))

    def add_sales(self, main_data: Dict[str, Any], items_data: List[Dict[str, Any]]) -> bool:
        """添加销售单（主表+明细表）"""
        try:
            self.begin_transaction()

            # 生成单据号
            sheet_id = self.generate_sheet_id()

            # 插入主表
            main_data['zth'] = self.zth
            main_data['sheetid'] = sheet_id
            main_data['flag_qr'] = '0'  # 未确认

            if not self.insert(self.main_table, main_data):
                self.rollback_transaction()
                return False

            # 插入明细表
            for i, item in enumerate(items_data, 1):
                item['zth'] = self.zth
                item['sheetid'] = sheet_id
                item['itemno'] = i

                if not self.insert(self.item_table, item):
                    self.rollback_transaction()
                    return False

            self.commit_transaction()
            logger.info(f"添加销售单成功: {sheet_id}")
            return True

        except Exception as e:
            self.rollback_transaction()
            logger.error(f"添加销售单失败: {e}")
            return False

    def update_sales(self, sheet_id: str, main_data: Dict[str, Any],
                    items_data: List[Dict[str, Any]]) -> bool:
        """更新销售单"""
        try:
            self.begin_transaction()

            # 检查是否已确认
            sales = self.get_sales_by_id(sheet_id)
            if sales and sales.get('flag_qr') == '1':
                logger.warning(f"销售单 {sheet_id} 已确认，不能修改")
                self.rollback_transaction()
                return False

            # 更新主表
            if not self.update(self.main_table, main_data,
                             'zth = %s AND sheetid = %s', (self.zth, sheet_id)):
                self.rollback_transaction()
                return False

            # 删除原明细
            if not self.delete(self.item_table, 'zth = %s AND sheetid = %s', (self.zth, sheet_id)):
                self.rollback_transaction()
                return False

            # 插入新明细
            for i, item in enumerate(items_data, 1):
                item['zth'] = self.zth
                item['sheetid'] = sheet_id
                item['itemno'] = i

                if not self.insert(self.item_table, item):
                    self.rollback_transaction()
                    return False

            self.commit_transaction()
            logger.info(f"更新销售单成功: {sheet_id}")
            return True

        except Exception as e:
            self.rollback_transaction()
            logger.error(f"更新销售单失败: {e}")
            return False

    def delete_sales(self, sheet_id: str) -> bool:
        """删除销售单"""
        try:
            self.begin_transaction()

            # 检查是否已确认
            sales = self.get_sales_by_id(sheet_id)
            if sales and sales.get('flag_qr') == '1':
                logger.warning(f"销售单 {sheet_id} 已确认，不能删除")
                self.rollback_transaction()
                return False

            # 删除明细
            if not self.delete(self.item_table, 'zth = %s AND sheetid = %s', (self.zth, sheet_id)):
                self.rollback_transaction()
                return False

            # 删除主表
            if not self.delete(self.main_table, 'zth = %s AND sheetid = %s', (self.zth, sheet_id)):
                self.rollback_transaction()
                return False

            self.commit_transaction()
            logger.info(f"删除销售单成功: {sheet_id}")
            return True

        except Exception as e:
            self.rollback_transaction()
            logger.error(f"删除销售单失败: {e}")
            return False

    def confirm_sales(self, sheet_id: str, oper_code: str) -> bool:
        """确认销售单"""
        try:
            data = {
                'flag_qr': '1',
                'oper_qr': oper_code,
                'date_qr': date.today()
            }

            result = self.update(self.main_table, data,
                               'zth = %s AND sheetid = %s AND flag_qr = %s',
                               (self.zth, sheet_id, '0'))

            if result:
                logger.info(f"确认销售单成功: {sheet_id}")
            else:
                logger.warning(f"确认销售单失败: {sheet_id}")

            return result

        except Exception as e:
            logger.error(f"确认销售单失败: {e}")
            return False
