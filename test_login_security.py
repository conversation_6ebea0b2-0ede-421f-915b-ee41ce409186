# -*- coding: utf-8 -*-
"""
登录安全机制测试脚本
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from models import OperatorDAO
from utils import logger

def test_login_attempts():
    """测试登录尝试次数限制"""
    print("=" * 50)
    print("测试登录安全机制...")
    
    operator_dao = OperatorDAO()
    
    # 测试正确登录
    print("\n1. 测试正确登录:")
    user_info = operator_dao.validate_login("admin", "123456")
    if user_info:
        print("✅ 正确用户名密码登录成功")
    else:
        print("❌ 正确用户名密码登录失败")
    
    # 测试错误密码
    print("\n2. 测试错误密码:")
    wrong_passwords = ["wrong1", "wrong2", "wrong3", "wrong4"]
    
    for i, wrong_password in enumerate(wrong_passwords, 1):
        user_info = operator_dao.validate_login("admin", wrong_password)
        if not user_info:
            print(f"✅ 第{i}次错误密码正确被拒绝")
        else:
            print(f"❌ 第{i}次错误密码应该被拒绝")
    
    # 测试不存在的用户
    print("\n3. 测试不存在的用户:")
    user_info = operator_dao.validate_login("nonexistent", "password")
    if not user_info:
        print("✅ 不存在的用户正确被拒绝")
    else:
        print("❌ 不存在的用户应该被拒绝")
    
    print("\n" + "=" * 50)
    print("登录安全机制测试完成")
    print("注意：实际的登录失败次数限制在UI层实现")
    print("建议手动测试UI界面的3次失败退出机制")

def test_password_validation():
    """测试密码验证逻辑"""
    print("\n" + "=" * 50)
    print("测试密码验证逻辑...")
    
    operator_dao = OperatorDAO()
    
    # 测试各种密码情况
    test_cases = [
        ("admin", "123456", True, "正确密码"),
        ("admin", "", False, "空密码"),
        ("admin", "wrong", False, "错误密码"),
        ("", "123456", False, "空用户名"),
        ("nonexistent", "123456", False, "不存在用户"),
        ("admin", "WRONG", False, "大写错误密码"),
        ("ADMIN", "123456", False, "大写用户名"),
    ]
    
    for username, password, expected, description in test_cases:
        try:
            result = operator_dao.validate_login(username, password)
            success = bool(result) == expected
            status = "✅" if success else "❌"
            print(f"{status} {description}: {'通过' if success else '失败'}")
        except Exception as e:
            print(f"❌ {description}: 异常 - {e}")

def main():
    """主测试函数"""
    print("JX进销存管理信息系统 - 登录安全测试")
    
    try:
        test_login_attempts()
        test_password_validation()
        
        print("\n" + "=" * 50)
        print("🔒 安全测试建议:")
        print("1. 手动测试UI界面的登录失败3次退出机制")
        print("2. 测试忘记密码功能")
        print("3. 测试登录界面的动画效果")
        print("4. 测试记住密码功能（如果实现）")
        print("5. 测试登录界面的关闭按钮")
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")

if __name__ == "__main__":
    main()
