# -*- coding: utf-8 -*-
"""
系统功能测试脚本
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from models import *
from config import db_config

def test_database_connection():
    """测试数据库连接"""
    print("=" * 50)
    print("测试数据库连接...")
    
    if db_config.test_connection():
        print("✅ 数据库连接成功")
        return True
    else:
        print("❌ 数据库连接失败")
        return False

def test_code_tables():
    """测试码表功能"""
    print("=" * 50)
    print("测试码表功能...")
    
    try:
        # 测试商品种类
        sort_dao = SortDAO()
        sorts = sort_dao.get_all_sorts()
        print(f"✅ 商品种类表查询成功，共 {len(sorts)} 条记录")
        
        # 测试计量单位
        unit_dao = UnitDAO()
        units = unit_dao.get_all_units()
        print(f"✅ 计量单位表查询成功，共 {len(units)} 条记录")
        
        # 测试产地
        aero_dao = AeroDAO()
        aeros = aero_dao.get_all_aeros()
        print(f"✅ 产地表查询成功，共 {len(aeros)} 条记录")
        
        # 测试部门
        dept_dao = DeptDAO()
        depts = dept_dao.get_all_depts()
        print(f"✅ 部门表查询成功，共 {len(depts)} 条记录")
        
        return True
        
    except Exception as e:
        print(f"❌ 码表功能测试失败: {e}")
        return False

def test_basic_info():
    """测试基本信息功能"""
    print("=" * 50)
    print("测试基本信息功能...")
    
    try:
        # 测试操作员
        operator_dao = OperatorDAO()
        operators = operator_dao.get_all_operators()
        print(f"✅ 操作员表查询成功，共 {len(operators)} 条记录")
        
        # 测试往来单位
        supp_dao = SuppDAO()
        supps = supp_dao.get_all_supps()
        print(f"✅ 往来单位表查询成功，共 {len(supps)} 条记录")
        
        # 测试商品信息
        goods_dao = GoodsDAO()
        goods = goods_dao.get_all_goods()
        print(f"✅ 商品信息表查询成功，共 {len(goods)} 条记录")
        
        return True
        
    except Exception as e:
        print(f"❌ 基本信息功能测试失败: {e}")
        return False

def test_crud_operations():
    """测试增删改查操作"""
    print("=" * 50)
    print("测试增删改查操作...")
    
    try:
        sort_dao = SortDAO()
        
        # 测试添加
        test_code = "99"
        test_name = "测试种类"
        
        # 如果已存在则先删除
        if sort_dao.sort_exists(test_code):
            sort_dao.delete_sort(test_code)
        
        # 添加测试数据
        if sort_dao.add_sort(test_code, test_name):
            print("✅ 添加操作成功")
        else:
            print("❌ 添加操作失败")
            return False
        
        # 测试查询
        sort_info = sort_dao.get_sort_by_code(test_code)
        if sort_info and sort_info['sort_name'] == test_name:
            print("✅ 查询操作成功")
        else:
            print("❌ 查询操作失败")
            return False
        
        # 测试修改
        new_name = "修改后的测试种类"
        if sort_dao.update_sort(test_code, new_name):
            print("✅ 修改操作成功")
        else:
            print("❌ 修改操作失败")
            return False
        
        # 验证修改结果
        sort_info = sort_dao.get_sort_by_code(test_code)
        if sort_info and sort_info['sort_name'] == new_name:
            print("✅ 修改结果验证成功")
        else:
            print("❌ 修改结果验证失败")
            return False
        
        # 测试删除
        if sort_dao.delete_sort(test_code):
            print("✅ 删除操作成功")
        else:
            print("❌ 删除操作失败")
            return False
        
        # 验证删除结果
        if not sort_dao.sort_exists(test_code):
            print("✅ 删除结果验证成功")
        else:
            print("❌ 删除结果验证失败")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 增删改查操作测试失败: {e}")
        return False

def test_login_function():
    """测试登录功能"""
    print("=" * 50)
    print("测试登录功能...")
    
    try:
        operator_dao = OperatorDAO()
        
        # 测试正确的用户名密码
        user_info = operator_dao.validate_login("admin", "123456")
        if user_info:
            print(f"✅ 登录验证成功，用户: {user_info['oper_name']}")
        else:
            print("❌ 登录验证失败")
            return False
        
        # 测试错误的密码
        user_info = operator_dao.validate_login("admin", "wrong_password")
        if not user_info:
            print("✅ 错误密码验证成功（正确拒绝）")
        else:
            print("❌ 错误密码验证失败（应该拒绝）")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 登录功能测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("JX进销存管理信息系统 - 功能测试")
    print("=" * 50)
    
    test_results = []
    
    # 执行各项测试
    test_results.append(("数据库连接", test_database_connection()))
    test_results.append(("码表功能", test_code_tables()))
    test_results.append(("基本信息功能", test_basic_info()))
    test_results.append(("增删改查操作", test_crud_operations()))
    test_results.append(("登录功能", test_login_function()))
    
    # 输出测试结果
    print("=" * 50)
    print("测试结果汇总:")
    print("=" * 50)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name:<15} {status}")
        if result:
            passed += 1
    
    print("=" * 50)
    print(f"测试完成: {passed}/{total} 项测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！系统功能正常。")
    else:
        print("⚠️  部分测试失败，请检查系统配置。")

if __name__ == "__main__":
    main()
