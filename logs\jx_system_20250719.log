2025-07-19 22:59:15,649 - utils.logger - INFO - 开始初始化数据库...
2025-07-19 22:59:15,657 - utils.logger - ERROR - 创建数据库失败: 'cryptography' package is required for sha256_password or caching_sha2_password auth methods
2025-07-19 22:59:15,657 - utils.logger - ERROR - 创建数据库失败
2025-07-19 22:59:56,367 - utils.logger - INFO - 开始初始化数据库...
2025-07-19 22:59:56,384 - utils.logger - ERROR - 创建数据库失败: (1045, "Access denied for user 'root'@'localhost' (using password: YES)")
2025-07-19 22:59:56,385 - utils.logger - ERROR - 创建数据库失败
2025-07-19 23:00:28,120 - utils.logger - INFO - 开始初始化数据库...
2025-07-19 23:00:28,125 - utils.logger - ERROR - 创建数据库失败: (1045, "Access denied for user 'root'@'localhost' (using password: NO)")
2025-07-19 23:00:28,125 - utils.logger - ERROR - 创建数据库失败
2025-07-19 23:01:30,572 - utils.logger - INFO - 开始初始化数据库...
2025-07-19 23:01:30,602 - utils.logger - INFO - 数据库 jx_inventory_system 创建成功
2025-07-19 23:01:30,604 - utils.logger - INFO - 执行建表脚本...
2025-07-19 23:01:30,605 - utils.logger - INFO - 执行SQL: USE jx_inventory_system...
2025-07-19 23:01:30,606 - utils.logger - INFO - 执行SQL: DROP TABLE IF EXISTS jx_sheet_xs_item...
2025-07-19 23:01:30,607 - utils.logger - INFO - 执行SQL: DROP TABLE IF EXISTS jx_sheet_xs_main...
2025-07-19 23:01:30,608 - utils.logger - INFO - 执行SQL: DROP TABLE IF EXISTS jx_sheet_cg_item...
2025-07-19 23:01:30,609 - utils.logger - INFO - 执行SQL: DROP TABLE IF EXISTS jx_sheet_cg_main...
2025-07-19 23:01:30,610 - utils.logger - INFO - 执行SQL: DROP TABLE IF EXISTS jx_sheet_bs_item...
2025-07-19 23:01:30,610 - utils.logger - INFO - 执行SQL: DROP TABLE IF EXISTS jx_sheet_bs_main...
2025-07-19 23:01:30,611 - utils.logger - INFO - 执行SQL: DROP TABLE IF EXISTS jx_sheet_pc_item...
2025-07-19 23:01:30,612 - utils.logger - INFO - 执行SQL: DROP TABLE IF EXISTS jx_sheet_pc_main...
2025-07-19 23:01:30,613 - utils.logger - INFO - 执行SQL: DROP TABLE IF EXISTS jx_sheet_fk...
2025-07-19 23:01:30,614 - utils.logger - INFO - 执行SQL: DROP TABLE IF EXISTS jx_sheet_sk...
2025-07-19 23:01:30,614 - utils.logger - INFO - 执行SQL: DROP TABLE IF EXISTS jx_table_goods_amount...
2025-07-19 23:01:30,615 - utils.logger - INFO - 执行SQL: DROP TABLE IF EXISTS jx_table_goods_record...
2025-07-19 23:01:30,616 - utils.logger - INFO - 执行SQL: DROP TABLE IF EXISTS jx_table_yf_mone...
2025-07-19 23:01:30,618 - utils.logger - INFO - 执行SQL: DROP TABLE IF EXISTS jx_table_ys_mone...
2025-07-19 23:01:30,619 - utils.logger - INFO - 执行SQL: DROP TABLE IF EXISTS jx_report_amount...
2025-07-19 23:01:30,620 - utils.logger - INFO - 执行SQL: DROP TABLE IF EXISTS jx_table_goods_price...
2025-07-19 23:01:30,621 - utils.logger - INFO - 执行SQL: DROP TABLE IF EXISTS jx_table_goods_price23...
2025-07-19 23:01:30,622 - utils.logger - INFO - 执行SQL: DROP TABLE IF EXISTS jx_table_goods_price123...
2025-07-19 23:01:30,623 - utils.logger - INFO - 执行SQL: DROP TABLE IF EXISTS jx_ini_goods_amount...
2025-07-19 23:01:30,624 - utils.logger - INFO - 执行SQL: DROP TABLE IF EXISTS jx_ini_yf_mone...
2025-07-19 23:01:30,625 - utils.logger - INFO - 执行SQL: DROP TABLE IF EXISTS jx_ini_ys_mone...
2025-07-19 23:01:30,625 - utils.logger - INFO - 执行SQL: DROP TABLE IF EXISTS jx_d_goods...
2025-07-19 23:01:30,626 - utils.logger - INFO - 执行SQL: DROP TABLE IF EXISTS jx_d_supp...
2025-07-19 23:01:30,627 - utils.logger - INFO - 执行SQL: DROP TABLE IF EXISTS jx_d_operator...
2025-07-19 23:01:30,628 - utils.logger - INFO - 执行SQL: DROP TABLE IF EXISTS jx_d_dept...
2025-07-19 23:01:30,629 - utils.logger - INFO - 执行SQL: DROP TABLE IF EXISTS jx_c_sort...
2025-07-19 23:01:30,629 - utils.logger - INFO - 执行SQL: DROP TABLE IF EXISTS jx_c_unit...
2025-07-19 23:01:30,630 - utils.logger - INFO - 执行SQL: DROP TABLE IF EXISTS jx_c_aero...
2025-07-19 23:01:30,631 - utils.logger - INFO - 执行SQL: DROP TABLE IF EXISTS zw_pz_zb...
2025-07-19 23:01:30,632 - utils.logger - INFO - 执行SQL: DROP TABLE IF EXISTS zw_zb_kmyeb...
2025-07-19 23:01:30,633 - utils.logger - INFO - 执行SQL: DROP TABLE IF EXISTS zw_zb_mxzb...
2025-07-19 23:01:30,634 - utils.logger - INFO - 执行SQL: DROP TABLE IF EXISTS zw_yb_zcfzb...
2025-07-19 23:01:30,635 - utils.logger - INFO - 执行SQL: DROP TABLE IF EXISTS zw_yb_lrb...
2025-07-19 23:01:30,636 - utils.logger - INFO - 执行SQL: DROP TABLE IF EXISTS zw_yb_cwzbtjb...
2025-07-19 23:01:30,637 - utils.logger - INFO - 执行SQL: DROP TABLE IF EXISTS zw_yb_dbfxb...
2025-07-19 23:01:30,638 - utils.logger - INFO - 执行SQL: DROP TABLE IF EXISTS zw_d_ztxxb...
2025-07-19 23:01:30,639 - utils.logger - INFO - 执行SQL: DROP TABLE IF EXISTS zw_d_kjkmbmb...
2025-07-19 23:01:30,639 - utils.logger - INFO - 执行SQL: DROP TABLE IF EXISTS zw_d_czy...
2025-07-19 23:01:30,640 - utils.logger - INFO - 执行SQL: DROP TABLE IF EXISTS zw_c_kmlb...
2025-07-19 23:01:30,641 - utils.logger - INFO - 执行SQL: DROP TABLE IF EXISTS zw_c_kmxz...
2025-07-19 23:01:30,642 - utils.logger - INFO - 执行SQL: DROP TABLE IF EXISTS zw_c_bb...
2025-07-19 23:01:30,643 - utils.logger - INFO - 执行SQL: DROP TABLE IF EXISTS zw_c_hy...
2025-07-19 23:01:30,644 - utils.logger - INFO - 执行SQL: DROP TABLE IF EXISTS zw_c_cwzbmb...
2025-07-19 23:01:30,644 - utils.logger - INFO - 建表脚本执行成功
2025-07-19 23:01:30,644 - utils.logger - INFO - 执行初始化数据脚本...
2025-07-19 23:01:30,644 - utils.logger - WARNING - 执行SQL失败: (1146, "Table 'jx_inventory_system.jx_c_sort' doesn't exist"), SQL: INSERT INTO jx_c_sort (sort_code, sort_name) VALUES ('01', '食品类')
2025-07-19 23:01:30,645 - utils.logger - WARNING - 执行SQL失败: (1146, "Table 'jx_inventory_system.jx_c_sort' doesn't exist"), SQL: INSERT INTO jx_c_sort (sort_code, sort_name) VALUES ('02', '服装类')
2025-07-19 23:01:30,645 - utils.logger - WARNING - 执行SQL失败: (1146, "Table 'jx_inventory_system.jx_c_sort' doesn't exist"), SQL: INSERT INTO jx_c_sort (sort_code, sort_name) VALUES ('03', '电器类')
2025-07-19 23:01:30,645 - utils.logger - WARNING - 执行SQL失败: (1146, "Table 'jx_inventory_system.jx_c_sort' doesn't exist"), SQL: INSERT INTO jx_c_sort (sort_code, sort_name) VALUES ('04', '日用品')
2025-07-19 23:01:30,645 - utils.logger - WARNING - 执行SQL失败: (1146, "Table 'jx_inventory_system.jx_c_aero' doesn't exist"), SQL: INSERT INTO jx_c_aero (aero_code, aero_name) VALUES ('01', '中国北京')
2025-07-19 23:01:30,645 - utils.logger - WARNING - 执行SQL失败: (1146, "Table 'jx_inventory_system.jx_c_aero' doesn't exist"), SQL: INSERT INTO jx_c_aero (aero_code, aero_name) VALUES ('02', '中国上海')
2025-07-19 23:01:30,646 - utils.logger - WARNING - 执行SQL失败: (1146, "Table 'jx_inventory_system.jx_c_aero' doesn't exist"), SQL: INSERT INTO jx_c_aero (aero_code, aero_name) VALUES ('03', '中国广州')
2025-07-19 23:01:30,646 - utils.logger - WARNING - 执行SQL失败: (1146, "Table 'jx_inventory_system.jx_c_aero' doesn't exist"), SQL: INSERT INTO jx_c_aero (aero_code, aero_name) VALUES ('04', '中国深圳')
2025-07-19 23:01:30,646 - utils.logger - WARNING - 执行SQL失败: (1146, "Table 'jx_inventory_system.jx_c_aero' doesn't exist"), SQL: INSERT INTO jx_c_aero (aero_code, aero_name) VALUES ('05', '中国杭州')
2025-07-19 23:01:30,646 - utils.logger - WARNING - 执行SQL失败: (1146, "Table 'jx_inventory_system.jx_c_aero' doesn't exist"), SQL: INSERT INTO jx_c_aero (aero_code, aero_name) VALUES ('06', '中国南京')
2025-07-19 23:01:30,647 - utils.logger - WARNING - 执行SQL失败: (1146, "Table 'jx_inventory_system.jx_c_aero' doesn't exist"), SQL: INSERT INTO jx_c_aero (aero_code, aero_name) VALUES ('07', '美国加州')
2025-07-19 23:01:30,647 - utils.logger - WARNING - 执行SQL失败: (1146, "Table 'jx_inventory_system.jx_c_unit' doesn't exist"), SQL: INSERT INTO jx_c_unit (unit_code, unit_name) VALUES ('01', '个')
2025-07-19 23:01:30,647 - utils.logger - WARNING - 执行SQL失败: (1146, "Table 'jx_inventory_system.jx_c_unit' doesn't exist"), SQL: INSERT INTO jx_c_unit (unit_code, unit_name) VALUES ('02', '件')
2025-07-19 23:01:30,647 - utils.logger - WARNING - 执行SQL失败: (1146, "Table 'jx_inventory_system.jx_c_unit' doesn't exist"), SQL: INSERT INTO jx_c_unit (unit_code, unit_name) VALUES ('03', '台')
2025-07-19 23:01:30,648 - utils.logger - WARNING - 执行SQL失败: (1146, "Table 'jx_inventory_system.jx_c_unit' doesn't exist"), SQL: INSERT INTO jx_c_unit (unit_code, unit_name) VALUES ('04', '套')
2025-07-19 23:01:30,648 - utils.logger - WARNING - 执行SQL失败: (1146, "Table 'jx_inventory_system.jx_d_dept' doesn't exist"), SQL: INSERT INTO jx_d_dept (dept_code, dept_name) VALUES ('01', '销售部')
2025-07-19 23:01:30,648 - utils.logger - WARNING - 执行SQL失败: (1146, "Table 'jx_inventory_system.jx_d_dept' doesn't exist"), SQL: INSERT INTO jx_d_dept (dept_code, dept_name) VALUES ('02', '采购部')
2025-07-19 23:01:30,648 - utils.logger - WARNING - 执行SQL失败: (1146, "Table 'jx_inventory_system.jx_d_dept' doesn't exist"), SQL: INSERT INTO jx_d_dept (dept_code, dept_name) VALUES ('03', '财务部')
2025-07-19 23:01:30,649 - utils.logger - WARNING - 执行SQL失败: (1146, "Table 'jx_inventory_system.jx_d_dept' doesn't exist"), SQL: INSERT INTO jx_d_dept (dept_code, dept_name) VALUES ('04', '仓储部')
2025-07-19 23:01:30,649 - utils.logger - WARNING - 执行SQL失败: (1146, "Table 'jx_inventory_system.jx_d_operator' doesn't exist"), SQL: INSERT INTO jx_d_operator (oper_code, oper_name, password, dept_code, power) VALUES ('admin', '系统管理员
2025-07-19 23:01:30,649 - utils.logger - WARNING - 执行SQL失败: (1146, "Table 'jx_inventory_system.jx_d_operator' doesn't exist"), SQL: INSERT INTO jx_d_operator (oper_code, oper_name, password, dept_code, power) VALUES ('sales01', '销售员
2025-07-19 23:01:30,649 - utils.logger - WARNING - 执行SQL失败: (1146, "Table 'jx_inventory_system.jx_d_operator' doesn't exist"), SQL: INSERT INTO jx_d_operator (oper_code, oper_name, password, dept_code, power) VALUES ('purchase01', '
2025-07-19 23:01:30,650 - utils.logger - WARNING - 执行SQL失败: (1146, "Table 'jx_inventory_system.jx_d_operator' doesn't exist"), SQL: INSERT INTO jx_d_operator (oper_code, oper_name, password, dept_code, power) VALUES ('finance01', '财
2025-07-19 23:01:30,650 - utils.logger - WARNING - 执行SQL失败: (1146, "Table 'jx_inventory_system.jx_d_supp' doesn't exist"), SQL: INSERT INTO jx_d_supp (supp_code, supp_name, zip, addr, tel, fax, email, web, account, bank) 
VALUES
2025-07-19 23:01:30,650 - utils.logger - WARNING - 执行SQL失败: (1146, "Table 'jx_inventory_system.jx_d_supp' doesn't exist"), SQL: INSERT INTO jx_d_supp (supp_code, supp_name, zip, addr, tel, fax, email, web, account, bank) 
VALUES
2025-07-19 23:01:30,650 - utils.logger - WARNING - 执行SQL失败: (1146, "Table 'jx_inventory_system.jx_d_supp' doesn't exist"), SQL: INSERT INTO jx_d_supp (supp_code, supp_name, zip, addr, tel, fax, email, web, account, bank) 
VALUES
2025-07-19 23:01:30,651 - utils.logger - WARNING - 执行SQL失败: (1146, "Table 'jx_inventory_system.jx_d_supp' doesn't exist"), SQL: INSERT INTO jx_d_supp (supp_code, supp_name, zip, addr, tel, fax, email, web, account, bank) 
VALUES
2025-07-19 23:01:30,651 - utils.logger - WARNING - 执行SQL失败: (1146, "Table 'jx_inventory_system.jx_d_goods' doesn't exist"), SQL: INSERT INTO jx_d_goods (goods_code, goods_name, sort_code, model, unit_code, price_retail, price_pla
2025-07-19 23:01:30,651 - utils.logger - WARNING - 执行SQL失败: (1146, "Table 'jx_inventory_system.jx_d_goods' doesn't exist"), SQL: INSERT INTO jx_d_goods (goods_code, goods_name, sort_code, model, unit_code, price_retail, price_pla
2025-07-19 23:01:30,651 - utils.logger - WARNING - 执行SQL失败: (1146, "Table 'jx_inventory_system.jx_d_goods' doesn't exist"), SQL: INSERT INTO jx_d_goods (goods_code, goods_name, sort_code, model, unit_code, price_retail, price_pla
2025-07-19 23:01:30,651 - utils.logger - WARNING - 执行SQL失败: (1146, "Table 'jx_inventory_system.jx_d_goods' doesn't exist"), SQL: INSERT INTO jx_d_goods (goods_code, goods_name, sort_code, model, unit_code, price_retail, price_pla
2025-07-19 23:01:30,652 - utils.logger - WARNING - 执行SQL失败: (1146, "Table 'jx_inventory_system.zw_c_kmlb' doesn't exist"), SQL: INSERT INTO zw_c_kmlb (kmlb_code, kmlb_name) VALUES ('01', '资产类')
2025-07-19 23:01:30,652 - utils.logger - WARNING - 执行SQL失败: (1146, "Table 'jx_inventory_system.zw_c_kmlb' doesn't exist"), SQL: INSERT INTO zw_c_kmlb (kmlb_code, kmlb_name) VALUES ('02', '负债类')
2025-07-19 23:01:30,652 - utils.logger - WARNING - 执行SQL失败: (1146, "Table 'jx_inventory_system.zw_c_kmlb' doesn't exist"), SQL: INSERT INTO zw_c_kmlb (kmlb_code, kmlb_name) VALUES ('03', '所有者权益')
2025-07-19 23:01:30,653 - utils.logger - WARNING - 执行SQL失败: (1146, "Table 'jx_inventory_system.zw_c_kmlb' doesn't exist"), SQL: INSERT INTO zw_c_kmlb (kmlb_code, kmlb_name) VALUES ('04', '成本类')
2025-07-19 23:01:30,653 - utils.logger - WARNING - 执行SQL失败: (1146, "Table 'jx_inventory_system.zw_c_kmlb' doesn't exist"), SQL: INSERT INTO zw_c_kmlb (kmlb_code, kmlb_name) VALUES ('05', '损益类')
2025-07-19 23:01:30,653 - utils.logger - WARNING - 执行SQL失败: (1146, "Table 'jx_inventory_system.zw_c_kmxz' doesn't exist"), SQL: INSERT INTO zw_c_kmxz (kmxz_code, kmxz_name) VALUES ('01', '借方')
2025-07-19 23:01:30,654 - utils.logger - WARNING - 执行SQL失败: (1146, "Table 'jx_inventory_system.zw_c_kmxz' doesn't exist"), SQL: INSERT INTO zw_c_kmxz (kmxz_code, kmxz_name) VALUES ('02', '贷方')
2025-07-19 23:01:30,654 - utils.logger - WARNING - 执行SQL失败: (1146, "Table 'jx_inventory_system.zw_c_bb' doesn't exist"), SQL: INSERT INTO zw_c_bb (bb_code, bb_name) VALUES ('01', '人民币')
2025-07-19 23:01:30,654 - utils.logger - WARNING - 执行SQL失败: (1146, "Table 'jx_inventory_system.zw_c_bb' doesn't exist"), SQL: INSERT INTO zw_c_bb (bb_code, bb_name) VALUES ('02', '美元')
2025-07-19 23:01:30,654 - utils.logger - WARNING - 执行SQL失败: (1146, "Table 'jx_inventory_system.zw_c_bb' doesn't exist"), SQL: INSERT INTO zw_c_bb (bb_code, bb_name) VALUES ('03', '欧元')
2025-07-19 23:01:30,655 - utils.logger - WARNING - 执行SQL失败: (1146, "Table 'jx_inventory_system.zw_c_hy' doesn't exist"), SQL: INSERT INTO zw_c_hy (hy_code, hy_name) VALUES ('01', '商业')
2025-07-19 23:01:30,656 - utils.logger - WARNING - 执行SQL失败: (1146, "Table 'jx_inventory_system.zw_c_hy' doesn't exist"), SQL: INSERT INTO zw_c_hy (hy_code, hy_name) VALUES ('02', '工业')
2025-07-19 23:01:30,656 - utils.logger - WARNING - 执行SQL失败: (1146, "Table 'jx_inventory_system.zw_c_hy' doesn't exist"), SQL: INSERT INTO zw_c_hy (hy_code, hy_name) VALUES ('03', '服务业')
2025-07-19 23:01:30,656 - utils.logger - WARNING - 执行SQL失败: (1146, "Table 'jx_inventory_system.zw_c_cwzbmb' doesn't exist"), SQL: INSERT INTO zw_c_cwzbmb (cwzb_code, cwzb_name, cwzb_dy, cwzb_jsgs) 
VALUES ('ROE', '净资产收益率', '反映企业盈利
2025-07-19 23:01:30,656 - utils.logger - WARNING - 执行SQL失败: (1146, "Table 'jx_inventory_system.zw_c_cwzbmb' doesn't exist"), SQL: INSERT INTO zw_c_cwzbmb (cwzb_code, cwzb_name, cwzb_dy, cwzb_jsgs) 
VALUES ('ROA', '总资产收益率', '反映企业资产
2025-07-19 23:01:30,656 - utils.logger - WARNING - 执行SQL失败: (1146, "Table 'jx_inventory_system.zw_c_cwzbmb' doesn't exist"), SQL: INSERT INTO zw_c_cwzbmb (cwzb_code, cwzb_name, cwzb_dy, cwzb_jsgs) 
VALUES ('CURRENT', '流动比率', '反映企业
2025-07-19 23:01:30,656 - utils.logger - WARNING - 执行SQL失败: (1146, "Table 'jx_inventory_system.zw_c_cwzbmb' doesn't exist"), SQL: INSERT INTO zw_c_cwzbmb (cwzb_code, cwzb_name, cwzb_dy, cwzb_jsgs) 
VALUES ('QUICK', '速动比率', '反映企业快速
2025-07-19 23:01:30,657 - utils.logger - WARNING - 执行SQL失败: (1146, "Table 'jx_inventory_system.zw_d_kjkmbmb' doesn't exist"), SQL: INSERT INTO zw_d_kjkmbmb (km_code, km_name, kmlb_code, kmxz_code, yefx) VALUES ('1001', '库存现金', '01'
2025-07-19 23:01:30,657 - utils.logger - WARNING - 执行SQL失败: (1146, "Table 'jx_inventory_system.zw_d_kjkmbmb' doesn't exist"), SQL: INSERT INTO zw_d_kjkmbmb (km_code, km_name, kmlb_code, kmxz_code, yefx) VALUES ('1002', '银行存款', '01'
2025-07-19 23:01:30,657 - utils.logger - WARNING - 执行SQL失败: (1146, "Table 'jx_inventory_system.zw_d_kjkmbmb' doesn't exist"), SQL: INSERT INTO zw_d_kjkmbmb (km_code, km_name, kmlb_code, kmxz_code, yefx) VALUES ('1121', '应收账款', '01'
2025-07-19 23:01:30,658 - utils.logger - WARNING - 执行SQL失败: (1146, "Table 'jx_inventory_system.zw_d_kjkmbmb' doesn't exist"), SQL: INSERT INTO zw_d_kjkmbmb (km_code, km_name, kmlb_code, kmxz_code, yefx) VALUES ('1401', '材料采购', '01'
2025-07-19 23:01:30,658 - utils.logger - WARNING - 执行SQL失败: (1146, "Table 'jx_inventory_system.zw_d_kjkmbmb' doesn't exist"), SQL: INSERT INTO zw_d_kjkmbmb (km_code, km_name, kmlb_code, kmxz_code, yefx) VALUES ('1402', '在途物资', '01'
2025-07-19 23:01:30,659 - utils.logger - WARNING - 执行SQL失败: (1146, "Table 'jx_inventory_system.zw_d_kjkmbmb' doesn't exist"), SQL: INSERT INTO zw_d_kjkmbmb (km_code, km_name, kmlb_code, kmxz_code, yefx) VALUES ('1403', '原材料', '01',
2025-07-19 23:01:30,659 - utils.logger - WARNING - 执行SQL失败: (1146, "Table 'jx_inventory_system.zw_d_kjkmbmb' doesn't exist"), SQL: INSERT INTO zw_d_kjkmbmb (km_code, km_name, kmlb_code, kmxz_code, yefx) VALUES ('1405', '库存商品', '01'
2025-07-19 23:01:30,659 - utils.logger - WARNING - 执行SQL失败: (1146, "Table 'jx_inventory_system.zw_d_kjkmbmb' doesn't exist"), SQL: INSERT INTO zw_d_kjkmbmb (km_code, km_name, kmlb_code, kmxz_code, yefx) VALUES ('2202', '应付账款', '02'
2025-07-19 23:01:30,659 - utils.logger - WARNING - 执行SQL失败: (1146, "Table 'jx_inventory_system.zw_d_kjkmbmb' doesn't exist"), SQL: INSERT INTO zw_d_kjkmbmb (km_code, km_name, kmlb_code, kmxz_code, yefx) VALUES ('4001', '生产成本', '04'
2025-07-19 23:01:30,660 - utils.logger - WARNING - 执行SQL失败: (1146, "Table 'jx_inventory_system.zw_d_kjkmbmb' doesn't exist"), SQL: INSERT INTO zw_d_kjkmbmb (km_code, km_name, kmlb_code, kmxz_code, yefx) VALUES ('6001', '主营业务收入', '0
2025-07-19 23:01:30,660 - utils.logger - WARNING - 执行SQL失败: (1146, "Table 'jx_inventory_system.zw_d_kjkmbmb' doesn't exist"), SQL: INSERT INTO zw_d_kjkmbmb (km_code, km_name, kmlb_code, kmxz_code, yefx) VALUES ('6401', '主营业务成本', '0
2025-07-19 23:01:30,660 - utils.logger - WARNING - 执行SQL失败: (1146, "Table 'jx_inventory_system.zw_d_czy' doesn't exist"), SQL: INSERT INTO zw_d_czy (czy_code, czy_name, mm) VALUES ('admin', '系统管理员', '123456')
2025-07-19 23:01:30,660 - utils.logger - WARNING - 执行SQL失败: (1146, "Table 'jx_inventory_system.zw_d_czy' doesn't exist"), SQL: INSERT INTO zw_d_czy (czy_code, czy_name, mm) VALUES ('finance01', '财务员1', '123456')
2025-07-19 23:01:30,660 - utils.logger - WARNING - 执行SQL失败: (1146, "Table 'jx_inventory_system.zw_d_ztxxb' doesn't exist"), SQL: INSERT INTO zw_d_ztxxb (zth, ztmc, hy_code, bb_code, qjs, qsrq, jsrq, qyrq, nkjqj, zt) 
VALUES ('01'
2025-07-19 23:01:30,660 - utils.logger - INFO - 初始化数据脚本执行成功
2025-07-19 23:01:30,660 - utils.logger - INFO - 数据库初始化完成！
2025-07-19 23:03:24,432 - utils.logger - INFO - 开始初始化数据库...
2025-07-19 23:03:24,440 - utils.logger - INFO - 数据库 jx_inventory_system 创建成功
2025-07-19 23:03:24,441 - utils.logger - INFO - 执行建表脚本...
2025-07-19 23:03:24,443 - utils.logger - INFO - 执行SQL: CREATE DATABASE IF NOT EXISTS jx_inventory_system ...
2025-07-19 23:03:24,443 - utils.logger - INFO - 执行SQL: USE jx_inventory_system;...
2025-07-19 23:03:24,444 - utils.logger - INFO - 执行SQL: DROP TABLE IF EXISTS jx_sheet_xs_item_cb;...
2025-07-19 23:03:24,445 - utils.logger - INFO - 执行SQL: DROP TABLE IF EXISTS jx_sheet_xs_item;...
2025-07-19 23:03:24,446 - utils.logger - INFO - 执行SQL: DROP TABLE IF EXISTS jx_sheet_xs_main;...
2025-07-19 23:03:24,447 - utils.logger - INFO - 执行SQL: DROP TABLE IF EXISTS jx_sheet_cg_item;...
2025-07-19 23:03:24,448 - utils.logger - INFO - 执行SQL: DROP TABLE IF EXISTS jx_sheet_cg_main;...
2025-07-19 23:03:24,449 - utils.logger - INFO - 执行SQL: DROP TABLE IF EXISTS jx_sheet_bs_item;...
2025-07-19 23:03:24,450 - utils.logger - INFO - 执行SQL: DROP TABLE IF EXISTS jx_sheet_bs_main;...
2025-07-19 23:03:24,451 - utils.logger - INFO - 执行SQL: DROP TABLE IF EXISTS jx_sheet_pc_item;...
2025-07-19 23:03:24,452 - utils.logger - INFO - 执行SQL: DROP TABLE IF EXISTS jx_sheet_pc_main;...
2025-07-19 23:03:24,453 - utils.logger - INFO - 执行SQL: DROP TABLE IF EXISTS jx_sheet_fk;...
2025-07-19 23:03:24,454 - utils.logger - INFO - 执行SQL: DROP TABLE IF EXISTS jx_sheet_sk;...
2025-07-19 23:03:24,456 - utils.logger - INFO - 执行SQL: DROP TABLE IF EXISTS jx_table_goods_amount;...
2025-07-19 23:03:24,458 - utils.logger - INFO - 执行SQL: DROP TABLE IF EXISTS jx_table_goods_record;...
2025-07-19 23:03:24,459 - utils.logger - INFO - 执行SQL: DROP TABLE IF EXISTS jx_table_yf_mone;...
2025-07-19 23:03:24,461 - utils.logger - INFO - 执行SQL: DROP TABLE IF EXISTS jx_table_ys_mone;...
2025-07-19 23:03:24,462 - utils.logger - INFO - 执行SQL: DROP TABLE IF EXISTS jx_report_amount;...
2025-07-19 23:03:24,463 - utils.logger - INFO - 执行SQL: DROP TABLE IF EXISTS jx_table_goods_price;...
2025-07-19 23:03:24,464 - utils.logger - INFO - 执行SQL: DROP TABLE IF EXISTS jx_table_goods_price23;...
2025-07-19 23:03:24,466 - utils.logger - INFO - 执行SQL: DROP TABLE IF EXISTS jx_table_goods_price123;...
2025-07-19 23:03:24,467 - utils.logger - INFO - 执行SQL: DROP TABLE IF EXISTS jx_ini_goods_amount;...
2025-07-19 23:03:24,469 - utils.logger - INFO - 执行SQL: DROP TABLE IF EXISTS jx_ini_yf_mone;...
2025-07-19 23:03:24,470 - utils.logger - INFO - 执行SQL: DROP TABLE IF EXISTS jx_ini_ys_mone;...
2025-07-19 23:03:24,471 - utils.logger - INFO - 执行SQL: DROP TABLE IF EXISTS jx_d_goods;...
2025-07-19 23:03:24,472 - utils.logger - INFO - 执行SQL: DROP TABLE IF EXISTS jx_d_supp;...
2025-07-19 23:03:24,473 - utils.logger - INFO - 执行SQL: DROP TABLE IF EXISTS jx_d_operator;...
2025-07-19 23:03:24,474 - utils.logger - INFO - 执行SQL: DROP TABLE IF EXISTS jx_d_dept;...
2025-07-19 23:03:24,475 - utils.logger - INFO - 执行SQL: DROP TABLE IF EXISTS jx_c_sort;...
2025-07-19 23:03:24,582 - utils.logger - INFO - 执行SQL: DROP TABLE IF EXISTS jx_c_unit;...
2025-07-19 23:03:24,584 - utils.logger - INFO - 执行SQL: DROP TABLE IF EXISTS jx_c_aero;...
2025-07-19 23:03:24,586 - utils.logger - INFO - 执行SQL: DROP TABLE IF EXISTS zw_pz_mxb;...
2025-07-19 23:03:24,588 - utils.logger - INFO - 执行SQL: DROP TABLE IF EXISTS zw_pz_zb;...
2025-07-19 23:03:24,590 - utils.logger - INFO - 执行SQL: DROP TABLE IF EXISTS zw_zb_kmyeb;...
2025-07-19 23:03:24,591 - utils.logger - INFO - 执行SQL: DROP TABLE IF EXISTS zw_zb_mxzb;...
2025-07-19 23:03:24,592 - utils.logger - INFO - 执行SQL: DROP TABLE IF EXISTS zw_yb_zcfzb;...
2025-07-19 23:03:24,593 - utils.logger - INFO - 执行SQL: DROP TABLE IF EXISTS zw_yb_lrb;...
2025-07-19 23:03:24,594 - utils.logger - INFO - 执行SQL: DROP TABLE IF EXISTS zw_yb_cwzbtjb;...
2025-07-19 23:03:24,595 - utils.logger - INFO - 执行SQL: DROP TABLE IF EXISTS zw_yb_dbfxb;...
2025-07-19 23:03:24,595 - utils.logger - INFO - 执行SQL: DROP TABLE IF EXISTS zw_d_ztxxb;...
2025-07-19 23:03:24,596 - utils.logger - INFO - 执行SQL: DROP TABLE IF EXISTS zw_d_kjkmbmb;...
2025-07-19 23:03:24,597 - utils.logger - INFO - 执行SQL: DROP TABLE IF EXISTS zw_d_czy;...
2025-07-19 23:03:24,598 - utils.logger - INFO - 执行SQL: DROP TABLE IF EXISTS zw_c_kmlb;...
2025-07-19 23:03:24,599 - utils.logger - INFO - 执行SQL: DROP TABLE IF EXISTS zw_c_kmxz;...
2025-07-19 23:03:24,600 - utils.logger - INFO - 执行SQL: DROP TABLE IF EXISTS zw_c_bb;...
2025-07-19 23:03:24,600 - utils.logger - INFO - 执行SQL: DROP TABLE IF EXISTS zw_c_hy;...
2025-07-19 23:03:24,601 - utils.logger - INFO - 执行SQL: DROP TABLE IF EXISTS zw_c_cwzbmb;...
2025-07-19 23:03:24,610 - utils.logger - INFO - 执行SQL: CREATE TABLE jx_c_sort ( sort_code CHAR(2) NOT NUL...
2025-07-19 23:03:24,617 - utils.logger - INFO - 执行SQL: CREATE TABLE jx_c_unit ( unit_code CHAR(2) NOT NUL...
2025-07-19 23:03:24,623 - utils.logger - INFO - 执行SQL: CREATE TABLE jx_c_aero ( aero_code CHAR(10) NOT NU...
2025-07-19 23:03:24,630 - utils.logger - INFO - 执行SQL: CREATE TABLE jx_d_dept ( dept_code CHAR(10) NOT NU...
2025-07-19 23:03:24,638 - utils.logger - INFO - 执行SQL: CREATE TABLE jx_d_operator ( oper_code CHAR(10) NO...
2025-07-19 23:03:24,645 - utils.logger - INFO - 执行SQL: CREATE TABLE jx_d_supp ( supp_code CHAR(10) NOT NU...
2025-07-19 23:03:24,657 - utils.logger - INFO - 执行SQL: CREATE TABLE jx_d_goods ( goods_code CHAR(13) NOT ...
2025-07-19 23:03:24,668 - utils.logger - INFO - 执行SQL: CREATE TABLE jx_ini_goods_amount ( zth CHAR(2) NOT...
2025-07-19 23:03:24,676 - utils.logger - INFO - 执行SQL: CREATE TABLE jx_ini_yf_mone ( zth CHAR(2) NOT NULL...
2025-07-19 23:03:24,683 - utils.logger - INFO - 执行SQL: CREATE TABLE jx_ini_ys_mone ( zth CHAR(2) NOT NULL...
2025-07-19 23:03:24,692 - utils.logger - INFO - 执行SQL: CREATE TABLE jx_sheet_cg_main ( zth CHAR(2) NOT NU...
2025-07-19 23:03:24,707 - utils.logger - INFO - 执行SQL: CREATE TABLE jx_sheet_cg_item ( zth CHAR(2) NOT NU...
2025-07-19 23:03:24,721 - utils.logger - INFO - 执行SQL: CREATE TABLE jx_sheet_xs_main ( zth CHAR(2) NOT NU...
2025-07-19 23:03:24,734 - utils.logger - INFO - 执行SQL: CREATE TABLE jx_sheet_xs_item ( zth CHAR(2) NOT NU...
2025-07-19 23:03:24,745 - utils.logger - INFO - 执行SQL: CREATE TABLE jx_sheet_xs_item_cb ( zth CHAR(2) NOT...
2025-07-19 23:03:24,753 - utils.logger - INFO - 执行SQL: CREATE TABLE jx_sheet_bs_main ( zth CHAR(2) NOT NU...
2025-07-19 23:03:24,763 - utils.logger - INFO - 执行SQL: CREATE TABLE jx_sheet_bs_item ( zth CHAR(2) NOT NU...
2025-07-19 23:03:24,772 - utils.logger - INFO - 执行SQL: CREATE TABLE jx_sheet_pc_main ( zth CHAR(2) NOT NU...
2025-07-19 23:03:24,783 - utils.logger - INFO - 执行SQL: CREATE TABLE jx_sheet_pc_item ( zth CHAR(2) NOT NU...
2025-07-19 23:03:24,795 - utils.logger - INFO - 执行SQL: CREATE TABLE jx_sheet_fk ( zth CHAR(2) NOT NULL CO...
2025-07-19 23:03:24,805 - utils.logger - INFO - 执行SQL: CREATE TABLE jx_sheet_sk ( zth CHAR(2) NOT NULL CO...
2025-07-19 23:03:24,814 - utils.logger - INFO - 执行SQL: CREATE TABLE jx_table_goods_amount ( zth CHAR(2) N...
2025-07-19 23:03:24,824 - utils.logger - INFO - 执行SQL: CREATE TABLE jx_table_yf_mone ( zth CHAR(2) NOT NU...
2025-07-19 23:03:24,832 - utils.logger - INFO - 执行SQL: CREATE TABLE jx_table_ys_mone ( zth CHAR(2) NOT NU...
2025-07-19 23:03:24,843 - utils.logger - INFO - 执行SQL: CREATE TABLE jx_report_amount ( zth CHAR(2) NOT NU...
2025-07-19 23:03:24,853 - utils.logger - INFO - 执行SQL: CREATE TABLE jx_table_goods_record ( zth CHAR(2) N...
2025-07-19 23:03:24,861 - utils.logger - INFO - 执行SQL: CREATE TABLE jx_table_goods_price ( zth CHAR(2) NO...
2025-07-19 23:03:24,872 - utils.logger - INFO - 执行SQL: CREATE TABLE jx_table_goods_price23 ( zth CHAR(2) ...
2025-07-19 23:03:24,882 - utils.logger - INFO - 执行SQL: CREATE TABLE jx_table_goods_price123 ( zth CHAR(2)...
2025-07-19 23:03:24,889 - utils.logger - INFO - 执行SQL: CREATE TABLE zw_c_kmlb ( kmlb_code CHAR(2) NOT NUL...
2025-07-19 23:03:24,899 - utils.logger - INFO - 执行SQL: CREATE TABLE zw_c_kmxz ( kmxz_code CHAR(2) NOT NUL...
2025-07-19 23:03:24,905 - utils.logger - INFO - 执行SQL: CREATE TABLE zw_c_bb ( bb_code CHAR(2) NOT NULL CO...
2025-07-19 23:03:24,912 - utils.logger - INFO - 执行SQL: CREATE TABLE zw_c_hy ( hy_code CHAR(10) NOT NULL C...
2025-07-19 23:03:24,920 - utils.logger - INFO - 执行SQL: CREATE TABLE zw_c_cwzbmb ( cwzb_code CHAR(10) NOT ...
2025-07-19 23:03:24,931 - utils.logger - INFO - 执行SQL: CREATE TABLE zw_d_ztxxb ( zth CHAR(2) NOT NULL COM...
2025-07-19 23:03:24,942 - utils.logger - INFO - 执行SQL: CREATE TABLE zw_d_kjkmbmb ( km_code CHAR(10) NOT N...
2025-07-19 23:03:24,949 - utils.logger - INFO - 执行SQL: CREATE TABLE zw_d_czy ( czy_code CHAR(10) NOT NULL...
2025-07-19 23:03:24,959 - utils.logger - INFO - 执行SQL: CREATE TABLE zw_pz_zb ( zth CHAR(2) NOT NULL COMME...
2025-07-19 23:03:24,971 - utils.logger - INFO - 执行SQL: CREATE TABLE zw_pz_mxb ( zth CHAR(2) NOT NULL COMM...
2025-07-19 23:03:24,981 - utils.logger - INFO - 执行SQL: CREATE TABLE zw_zb_kmyeb ( zth CHAR(2) NOT NULL CO...
2025-07-19 23:03:24,992 - utils.logger - INFO - 执行SQL: CREATE TABLE zw_zb_mxzb ( zth CHAR(2) NOT NULL COM...
2025-07-19 23:03:25,004 - utils.logger - INFO - 执行SQL: CREATE TABLE zw_yb_zcfzb ( zth CHAR(2) NOT NULL CO...
2025-07-19 23:03:25,015 - utils.logger - INFO - 执行SQL: CREATE TABLE zw_yb_lrb ( zth CHAR(2) NOT NULL COMM...
2025-07-19 23:03:25,024 - utils.logger - INFO - 执行SQL: CREATE TABLE zw_yb_cwzbtjb ( zth CHAR(2) NOT NULL ...
2025-07-19 23:03:25,034 - utils.logger - INFO - 执行SQL: CREATE TABLE zw_yb_dbfxb ( zth CHAR(2) NOT NULL CO...
2025-07-19 23:03:25,034 - utils.logger - INFO - 建表脚本执行成功
2025-07-19 23:03:25,034 - utils.logger - INFO - 执行初始化数据脚本...
2025-07-19 23:03:25,035 - utils.logger - INFO - 执行SQL: USE jx_inventory_system;...
2025-07-19 23:03:25,038 - utils.logger - INFO - 执行SQL: DELETE FROM jx_c_sort;...
2025-07-19 23:03:25,038 - utils.logger - INFO - 执行SQL: INSERT INTO jx_c_sort (sort_code, sort_name) VALUE...
2025-07-19 23:03:25,038 - utils.logger - INFO - 执行SQL: INSERT INTO jx_c_sort (sort_code, sort_name) VALUE...
2025-07-19 23:03:25,038 - utils.logger - INFO - 执行SQL: INSERT INTO jx_c_sort (sort_code, sort_name) VALUE...
2025-07-19 23:03:25,039 - utils.logger - INFO - 执行SQL: INSERT INTO jx_c_sort (sort_code, sort_name) VALUE...
2025-07-19 23:03:25,039 - utils.logger - INFO - 执行SQL: DELETE FROM jx_c_aero;...
2025-07-19 23:03:25,039 - utils.logger - INFO - 执行SQL: INSERT INTO jx_c_aero (aero_code, aero_name) VALUE...
2025-07-19 23:03:25,039 - utils.logger - INFO - 执行SQL: INSERT INTO jx_c_aero (aero_code, aero_name) VALUE...
2025-07-19 23:03:25,039 - utils.logger - INFO - 执行SQL: INSERT INTO jx_c_aero (aero_code, aero_name) VALUE...
2025-07-19 23:03:25,039 - utils.logger - INFO - 执行SQL: INSERT INTO jx_c_aero (aero_code, aero_name) VALUE...
2025-07-19 23:03:25,041 - utils.logger - INFO - 执行SQL: INSERT INTO jx_c_aero (aero_code, aero_name) VALUE...
2025-07-19 23:03:25,041 - utils.logger - INFO - 执行SQL: INSERT INTO jx_c_aero (aero_code, aero_name) VALUE...
2025-07-19 23:03:25,041 - utils.logger - INFO - 执行SQL: INSERT INTO jx_c_aero (aero_code, aero_name) VALUE...
2025-07-19 23:03:25,041 - utils.logger - INFO - 执行SQL: DELETE FROM jx_c_unit;...
2025-07-19 23:03:25,042 - utils.logger - INFO - 执行SQL: INSERT INTO jx_c_unit (unit_code, unit_name) VALUE...
2025-07-19 23:03:25,042 - utils.logger - INFO - 执行SQL: INSERT INTO jx_c_unit (unit_code, unit_name) VALUE...
2025-07-19 23:03:25,042 - utils.logger - INFO - 执行SQL: INSERT INTO jx_c_unit (unit_code, unit_name) VALUE...
2025-07-19 23:03:25,043 - utils.logger - INFO - 执行SQL: INSERT INTO jx_c_unit (unit_code, unit_name) VALUE...
2025-07-19 23:03:25,043 - utils.logger - INFO - 执行SQL: DELETE FROM jx_d_dept;...
2025-07-19 23:03:25,043 - utils.logger - INFO - 执行SQL: INSERT INTO jx_d_dept (dept_code, dept_name) VALUE...
2025-07-19 23:03:25,044 - utils.logger - INFO - 执行SQL: INSERT INTO jx_d_dept (dept_code, dept_name) VALUE...
2025-07-19 23:03:25,044 - utils.logger - INFO - 执行SQL: INSERT INTO jx_d_dept (dept_code, dept_name) VALUE...
2025-07-19 23:03:25,044 - utils.logger - INFO - 执行SQL: INSERT INTO jx_d_dept (dept_code, dept_name) VALUE...
2025-07-19 23:03:25,045 - utils.logger - INFO - 执行SQL: DELETE FROM jx_d_operator;...
2025-07-19 23:03:25,045 - utils.logger - INFO - 执行SQL: INSERT INTO jx_d_operator (oper_code, oper_name, p...
2025-07-19 23:03:25,045 - utils.logger - INFO - 执行SQL: INSERT INTO jx_d_operator (oper_code, oper_name, p...
2025-07-19 23:03:25,045 - utils.logger - INFO - 执行SQL: INSERT INTO jx_d_operator (oper_code, oper_name, p...
2025-07-19 23:03:25,046 - utils.logger - INFO - 执行SQL: INSERT INTO jx_d_operator (oper_code, oper_name, p...
2025-07-19 23:03:25,046 - utils.logger - INFO - 执行SQL: DELETE FROM jx_d_supp;...
2025-07-19 23:03:25,046 - utils.logger - INFO - 执行SQL: INSERT INTO jx_d_supp (supp_code, supp_name, zip, ...
2025-07-19 23:03:25,046 - utils.logger - INFO - 执行SQL: INSERT INTO jx_d_supp (supp_code, supp_name, zip, ...
2025-07-19 23:03:25,048 - utils.logger - INFO - 执行SQL: INSERT INTO jx_d_supp (supp_code, supp_name, zip, ...
2025-07-19 23:03:25,048 - utils.logger - INFO - 执行SQL: INSERT INTO jx_d_supp (supp_code, supp_name, zip, ...
2025-07-19 23:03:25,049 - utils.logger - INFO - 执行SQL: DELETE FROM jx_d_goods;...
2025-07-19 23:03:25,049 - utils.logger - INFO - 执行SQL: INSERT INTO jx_d_goods (goods_code, goods_name, so...
2025-07-19 23:03:25,049 - utils.logger - INFO - 执行SQL: INSERT INTO jx_d_goods (goods_code, goods_name, so...
2025-07-19 23:03:25,050 - utils.logger - INFO - 执行SQL: INSERT INTO jx_d_goods (goods_code, goods_name, so...
2025-07-19 23:03:25,050 - utils.logger - INFO - 执行SQL: INSERT INTO jx_d_goods (goods_code, goods_name, so...
2025-07-19 23:03:25,051 - utils.logger - INFO - 执行SQL: DELETE FROM zw_c_kmlb;...
2025-07-19 23:03:25,051 - utils.logger - INFO - 执行SQL: INSERT INTO zw_c_kmlb (kmlb_code, kmlb_name) VALUE...
2025-07-19 23:03:25,052 - utils.logger - INFO - 执行SQL: INSERT INTO zw_c_kmlb (kmlb_code, kmlb_name) VALUE...
2025-07-19 23:03:25,052 - utils.logger - INFO - 执行SQL: INSERT INTO zw_c_kmlb (kmlb_code, kmlb_name) VALUE...
2025-07-19 23:03:25,052 - utils.logger - INFO - 执行SQL: INSERT INTO zw_c_kmlb (kmlb_code, kmlb_name) VALUE...
2025-07-19 23:03:25,052 - utils.logger - INFO - 执行SQL: INSERT INTO zw_c_kmlb (kmlb_code, kmlb_name) VALUE...
2025-07-19 23:03:25,053 - utils.logger - INFO - 执行SQL: DELETE FROM zw_c_kmxz;...
2025-07-19 23:03:25,053 - utils.logger - INFO - 执行SQL: INSERT INTO zw_c_kmxz (kmxz_code, kmxz_name) VALUE...
2025-07-19 23:03:25,053 - utils.logger - INFO - 执行SQL: INSERT INTO zw_c_kmxz (kmxz_code, kmxz_name) VALUE...
2025-07-19 23:03:25,054 - utils.logger - INFO - 执行SQL: DELETE FROM zw_c_bb;...
2025-07-19 23:03:25,054 - utils.logger - INFO - 执行SQL: INSERT INTO zw_c_bb (bb_code, bb_name) VALUES ('01...
2025-07-19 23:03:25,054 - utils.logger - INFO - 执行SQL: INSERT INTO zw_c_bb (bb_code, bb_name) VALUES ('02...
2025-07-19 23:03:25,054 - utils.logger - INFO - 执行SQL: INSERT INTO zw_c_bb (bb_code, bb_name) VALUES ('03...
2025-07-19 23:03:25,055 - utils.logger - INFO - 执行SQL: DELETE FROM zw_c_hy;...
2025-07-19 23:03:25,055 - utils.logger - INFO - 执行SQL: INSERT INTO zw_c_hy (hy_code, hy_name) VALUES ('01...
2025-07-19 23:03:25,055 - utils.logger - INFO - 执行SQL: INSERT INTO zw_c_hy (hy_code, hy_name) VALUES ('02...
2025-07-19 23:03:25,055 - utils.logger - INFO - 执行SQL: INSERT INTO zw_c_hy (hy_code, hy_name) VALUES ('03...
2025-07-19 23:03:25,055 - utils.logger - INFO - 执行SQL: DELETE FROM zw_c_cwzbmb;...
2025-07-19 23:03:25,056 - utils.logger - INFO - 执行SQL: INSERT INTO zw_c_cwzbmb (cwzb_code, cwzb_name, cwz...
2025-07-19 23:03:25,056 - utils.logger - INFO - 执行SQL: INSERT INTO zw_c_cwzbmb (cwzb_code, cwzb_name, cwz...
2025-07-19 23:03:25,056 - utils.logger - INFO - 执行SQL: INSERT INTO zw_c_cwzbmb (cwzb_code, cwzb_name, cwz...
2025-07-19 23:03:25,057 - utils.logger - INFO - 执行SQL: INSERT INTO zw_c_cwzbmb (cwzb_code, cwzb_name, cwz...
2025-07-19 23:03:25,057 - utils.logger - INFO - 执行SQL: DELETE FROM zw_d_kjkmbmb;...
2025-07-19 23:03:25,058 - utils.logger - INFO - 执行SQL: INSERT INTO zw_d_kjkmbmb (km_code, km_name, kmlb_c...
2025-07-19 23:03:25,058 - utils.logger - INFO - 执行SQL: INSERT INTO zw_d_kjkmbmb (km_code, km_name, kmlb_c...
2025-07-19 23:03:25,058 - utils.logger - INFO - 执行SQL: INSERT INTO zw_d_kjkmbmb (km_code, km_name, kmlb_c...
2025-07-19 23:03:25,058 - utils.logger - INFO - 执行SQL: INSERT INTO zw_d_kjkmbmb (km_code, km_name, kmlb_c...
2025-07-19 23:03:25,058 - utils.logger - INFO - 执行SQL: INSERT INTO zw_d_kjkmbmb (km_code, km_name, kmlb_c...
2025-07-19 23:03:25,059 - utils.logger - INFO - 执行SQL: INSERT INTO zw_d_kjkmbmb (km_code, km_name, kmlb_c...
2025-07-19 23:03:25,059 - utils.logger - INFO - 执行SQL: INSERT INTO zw_d_kjkmbmb (km_code, km_name, kmlb_c...
2025-07-19 23:03:25,059 - utils.logger - INFO - 执行SQL: INSERT INTO zw_d_kjkmbmb (km_code, km_name, kmlb_c...
2025-07-19 23:03:25,059 - utils.logger - INFO - 执行SQL: INSERT INTO zw_d_kjkmbmb (km_code, km_name, kmlb_c...
2025-07-19 23:03:25,060 - utils.logger - INFO - 执行SQL: INSERT INTO zw_d_kjkmbmb (km_code, km_name, kmlb_c...
2025-07-19 23:03:25,060 - utils.logger - INFO - 执行SQL: INSERT INTO zw_d_kjkmbmb (km_code, km_name, kmlb_c...
2025-07-19 23:03:25,060 - utils.logger - INFO - 执行SQL: DELETE FROM zw_d_czy;...
2025-07-19 23:03:25,061 - utils.logger - INFO - 执行SQL: INSERT INTO zw_d_czy (czy_code, czy_name, mm) VALU...
2025-07-19 23:03:25,061 - utils.logger - INFO - 执行SQL: INSERT INTO zw_d_czy (czy_code, czy_name, mm) VALU...
2025-07-19 23:03:25,062 - utils.logger - INFO - 执行SQL: DELETE FROM zw_d_ztxxb;...
2025-07-19 23:03:25,062 - utils.logger - INFO - 执行SQL: INSERT INTO zw_d_ztxxb (zth, ztmc, hy_code, bb_cod...
2025-07-19 23:03:25,063 - utils.logger - INFO - 初始化数据脚本执行成功
2025-07-19 23:03:25,063 - utils.logger - INFO - 数据库初始化完成！
2025-07-19 23:06:44,632 - utils.logger - INFO - 数据库连接成功
2025-07-19 23:06:51,410 - utils.logger - INFO - 用户 admin 登录成功
2025-07-19 23:06:55,126 - utils.logger - INFO - 用户 admin 登录成功
2025-07-19 23:06:58,757 - utils.logger - INFO - 用户 admin 登录成功
2025-07-19 23:11:45,071 - utils.logger - INFO - 数据库连接成功
2025-07-19 23:11:48,822 - utils.logger - INFO - 用户 admin 登录成功
2025-07-19 23:11:50,655 - utils.logger - ERROR - 加载商品数据失败: 'NoneType' object has no attribute 'get_all_goods'
2025-07-19 23:14:18,810 - utils.logger - INFO - 数据库连接成功
2025-07-19 23:14:24,526 - utils.logger - INFO - 用户 admin 登录成功
2025-07-19 23:43:31,671 - utils.logger - INFO - 数据库连接成功
2025-07-19 23:43:40,145 - utils.logger - INFO - 用户 admin 登录成功
2025-07-19 23:45:56,662 - utils.logger - INFO - 数据库连接成功
2025-07-19 23:46:20,771 - utils.logger - INFO - 用户取消登录，程序退出
2025-07-19 23:49:29,423 - utils.logger - INFO - 数据库连接成功
2025-07-19 23:54:03,460 - utils.logger - INFO - 数据库连接成功
2025-07-19 23:54:28,266 - utils.logger - INFO - 用户取消登录，程序退出
2025-07-19 23:56:16,187 - utils.logger - INFO - 数据库连接成功
2025-07-19 23:57:04,218 - utils.logger - INFO - 用户取消登录，程序退出
2025-07-19 23:58:38,473 - utils.logger - INFO - 数据库连接成功
2025-07-19 23:58:42,746 - utils.logger - INFO - 用户取消登录，程序退出
