# -*- coding: utf-8 -*-
"""
JX进销存管理信息系统主程序
"""

import sys
import os
from PyQt5.QtWidgets import QApplication, QMessageBox, QSplashScreen
from PyQt5.QtCore import Qt, QTimer
from PyQt5.QtGui import QPixmap, QFont
from ui import MainWindow
from config import db_config
from utils import logger

def check_database_connection():
    """检查数据库连接"""
    try:
        if db_config.test_connection():
            logger.info("数据库连接成功")
            return True
        else:
            logger.error("数据库连接失败")
            return False
    except Exception as e:
        logger.error(f"数据库连接检查失败: {e}")
        return False

def create_splash_screen():
    """创建启动画面"""
    # 创建一个简单的启动画面
    splash_pix = QPixmap(400, 300)
    splash_pix.fill(Qt.white)
    
    splash = QSplashScreen(splash_pix, Qt.WindowStaysOnTopHint)
    splash.setMask(splash_pix.mask())
    
    # 显示启动信息
    splash.showMessage("正在启动JX进销存管理信息系统...", 
                      Qt.AlignHCenter | Qt.AlignBottom, Qt.black)
    
    return splash

def main():
    """主函数"""
    # 创建应用程序
    app = QApplication(sys.argv)
    app.setApplicationName("JX进销存管理信息系统")
    app.setApplicationVersion("1.0")
    app.setOrganizationName("JX公司")
    
    # 设置应用程序字体
    font = QFont("Microsoft YaHei", 9)
    app.setFont(font)
    
    # 创建启动画面
    splash = create_splash_screen()
    splash.show()
    
    # 处理启动画面显示
    app.processEvents()
    
    try:
        # 检查数据库连接
        splash.showMessage("正在检查数据库连接...", 
                          Qt.AlignHCenter | Qt.AlignBottom, Qt.black)
        app.processEvents()
        
        if not check_database_connection():
            splash.close()
            QMessageBox.critical(None, "错误", 
                               "无法连接到数据库！\n"
                               "请检查数据库配置和连接。\n\n"
                               "配置文件: config/database.py")
            return 1
        
        # 创建主窗口
        splash.showMessage("正在初始化主界面...", 
                          Qt.AlignHCenter | Qt.AlignBottom, Qt.black)
        app.processEvents()
        
        main_window = MainWindow()
        
        # 延迟显示登录对话框
        def show_login_first():
            splash.close()
            # 先显示登录对话框
            from ui.login_dialog import LoginDialog
            login_dialog = LoginDialog()
            result = login_dialog.exec_()

            if result == login_dialog.Accepted:
                user_info = login_dialog.get_user_info()
                main_window.set_current_user(user_info)
                main_window.show()
                main_window.statusbar.showMessage("登录成功，欢迎使用JX进销存管理信息系统！", 5000)
            else:
                # 用户取消登录或登录失败，退出程序
                logger.info("用户取消登录，程序退出")
                app.quit()

        QTimer.singleShot(2000, show_login_first)
        
        # 运行应用程序
        return app.exec_()
        
    except Exception as e:
        splash.close()
        logger.error(f"应用程序启动失败: {e}")
        QMessageBox.critical(None, "错误", f"应用程序启动失败:\n{str(e)}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
