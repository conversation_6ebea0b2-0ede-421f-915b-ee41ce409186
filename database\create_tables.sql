-- JX进销存管理信息系统数据库表结构
-- 创建数据库
CREATE DATABASE IF NOT EXISTS jx_inventory_system DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE jx_inventory_system;

-- 删除已存在的表（按依赖关系倒序删除）
DROP TABLE IF EXISTS jx_sheet_xs_item_cb;
DROP TABLE IF EXISTS jx_sheet_xs_item;
DROP TABLE IF EXISTS jx_sheet_xs_main;
DROP TABLE IF EXISTS jx_sheet_cg_item;
DROP TABLE IF EXISTS jx_sheet_cg_main;
DROP TABLE IF EXISTS jx_sheet_bs_item;
DROP TABLE IF EXISTS jx_sheet_bs_main;
DROP TABLE IF EXISTS jx_sheet_pc_item;
DROP TABLE IF EXISTS jx_sheet_pc_main;
DROP TABLE IF EXISTS jx_sheet_fk;
DROP TABLE IF EXISTS jx_sheet_sk;
DROP TABLE IF EXISTS jx_table_goods_amount;
DROP TABLE IF EXISTS jx_table_goods_record;
DROP TABLE IF EXISTS jx_table_yf_mone;
DROP TABLE IF EXISTS jx_table_ys_mone;
DROP TABLE IF EXISTS jx_report_amount;
DROP TABLE IF EXISTS jx_table_goods_price;
DROP TABLE IF EXISTS jx_table_goods_price23;
DROP TABLE IF EXISTS jx_table_goods_price123;
DROP TABLE IF EXISTS jx_ini_goods_amount;
DROP TABLE IF EXISTS jx_ini_yf_mone;
DROP TABLE IF EXISTS jx_ini_ys_mone;
DROP TABLE IF EXISTS jx_d_goods;
DROP TABLE IF EXISTS jx_d_supp;
DROP TABLE IF EXISTS jx_d_operator;
DROP TABLE IF EXISTS jx_d_dept;
DROP TABLE IF EXISTS jx_c_sort;
DROP TABLE IF EXISTS jx_c_unit;
DROP TABLE IF EXISTS jx_c_aero;

-- 会计系统表
DROP TABLE IF EXISTS zw_pz_mxb;
DROP TABLE IF EXISTS zw_pz_zb;
DROP TABLE IF EXISTS zw_zb_kmyeb;
DROP TABLE IF EXISTS zw_zb_mxzb;
DROP TABLE IF EXISTS zw_yb_zcfzb;
DROP TABLE IF EXISTS zw_yb_lrb;
DROP TABLE IF EXISTS zw_yb_cwzbtjb;
DROP TABLE IF EXISTS zw_yb_dbfxb;
DROP TABLE IF EXISTS zw_d_ztxxb;
DROP TABLE IF EXISTS zw_d_kjkmbmb;
DROP TABLE IF EXISTS zw_d_czy;
DROP TABLE IF EXISTS zw_c_kmlb;
DROP TABLE IF EXISTS zw_c_kmxz;
DROP TABLE IF EXISTS zw_c_bb;
DROP TABLE IF EXISTS zw_c_hy;
DROP TABLE IF EXISTS zw_c_cwzbmb;

-- ========== 码表 ==========
-- 商品种类码表
CREATE TABLE jx_c_sort (
    sort_code CHAR(2) NOT NULL COMMENT '种类代码',
    sort_name VARCHAR(50) COMMENT '种类名称',
    PRIMARY KEY (sort_code)
) COMMENT='商品种类码表';

-- 商品计量单位码表
CREATE TABLE jx_c_unit (
    unit_code CHAR(2) NOT NULL COMMENT '单位代码',
    unit_name VARCHAR(10) COMMENT '单位名称',
    PRIMARY KEY (unit_code)
) COMMENT='商品计量单位码表';

-- 商品产地码表
CREATE TABLE jx_c_aero (
    aero_code CHAR(10) NOT NULL COMMENT '产地代码',
    aero_name VARCHAR(100) COMMENT '产地名称',
    PRIMARY KEY (aero_code)
) COMMENT='商品产地码表';

-- ========== 基本信息表 ==========
-- 部门信息表
CREATE TABLE jx_d_dept (
    dept_code CHAR(10) NOT NULL COMMENT '部门代码',
    dept_name VARCHAR(50) COMMENT '部门名称',
    PRIMARY KEY (dept_code)
) COMMENT='部门信息表';

-- 操作员信息表
CREATE TABLE jx_d_operator (
    oper_code CHAR(10) NOT NULL COMMENT '操作员代码',
    oper_name VARCHAR(20) COMMENT '操作员姓名',
    password VARCHAR(50) COMMENT '密码',
    dept_code CHAR(10) COMMENT '部门代码',
    power VARCHAR(100) COMMENT '权限',
    PRIMARY KEY (oper_code),
    FOREIGN KEY (dept_code) REFERENCES jx_d_dept(dept_code)
) COMMENT='操作员信息表';

-- 往来单位信息表
CREATE TABLE jx_d_supp (
    supp_code CHAR(10) NOT NULL COMMENT '单位代码',
    supp_name VARCHAR(100) COMMENT '单位名称',
    zip CHAR(6) COMMENT '邮编',
    addr VARCHAR(100) COMMENT '地址',
    tel VARCHAR(20) COMMENT '电话',
    fax VARCHAR(20) COMMENT '传真',
    email VARCHAR(100) COMMENT '邮箱',
    web VARCHAR(100) COMMENT '网址',
    account VARCHAR(30) COMMENT '银行账号',
    bank VARCHAR(100) COMMENT '开户银行',
    PRIMARY KEY (supp_code)
) COMMENT='往来单位信息表';

-- 商品信息表
CREATE TABLE jx_d_goods (
    goods_code CHAR(13) NOT NULL COMMENT '商品代码',
    goods_name VARCHAR(60) COMMENT '商品名称',
    sort_code CHAR(2) COMMENT '种类代码',
    model VARCHAR(50) COMMENT '规格型号',
    unit_code CHAR(2) COMMENT '单位代码',
    price_retail DECIMAL(8,2) COMMENT '零售价',
    price_plan DECIMAL(8,2) COMMENT '计划价',
    aero_code CHAR(10) COMMENT '产地代码',
    supp_code CHAR(10) COMMENT '供应商代码',
    note TEXT COMMENT '备注',
    photofile VARCHAR(100) COMMENT '图片文件',
    PRIMARY KEY (goods_code),
    FOREIGN KEY (sort_code) REFERENCES jx_c_sort(sort_code),
    FOREIGN KEY (unit_code) REFERENCES jx_c_unit(unit_code),
    FOREIGN KEY (aero_code) REFERENCES jx_c_aero(aero_code),
    FOREIGN KEY (supp_code) REFERENCES jx_d_supp(supp_code)
) COMMENT='商品信息表';

-- ========== 初始化表 ==========
-- 期初商品数量表
CREATE TABLE jx_ini_goods_amount (
    zth CHAR(2) NOT NULL COMMENT '账套号',
    goods_code CHAR(13) NOT NULL COMMENT '商品代码',
    amount INTEGER COMMENT '数量',
    mone DECIMAL(10,2) COMMENT '金额',
    flag_qr CHAR(2) COMMENT '确认标志',
    oper_qr CHAR(10) COMMENT '确认操作员',
    date_qr DATE COMMENT '确认日期',
    PRIMARY KEY (zth, goods_code),
    FOREIGN KEY (goods_code) REFERENCES jx_d_goods(goods_code)
) COMMENT='期初商品数量表';

-- 期初应付款表
CREATE TABLE jx_ini_yf_mone (
    zth CHAR(2) NOT NULL COMMENT '账套号',
    supp_code CHAR(10) NOT NULL COMMENT '供应商代码',
    mone_yf DECIMAL(10,2) COMMENT '应付金额',
    mone_fk DECIMAL(10,2) COMMENT '付款金额',
    mone_ye DECIMAL(10,2) COMMENT '余额',
    note TEXT COMMENT '备注',
    flag_qr CHAR(2) COMMENT '确认标志',
    oper_qr CHAR(10) COMMENT '确认操作员',
    date_qr DATE COMMENT '确认日期',
    PRIMARY KEY (zth, supp_code),
    FOREIGN KEY (supp_code) REFERENCES jx_d_supp(supp_code)
) COMMENT='期初应付款表';

-- 期初应收款表
CREATE TABLE jx_ini_ys_mone (
    zth CHAR(2) NOT NULL COMMENT '账套号',
    sheetid_xs CHAR(13) NOT NULL COMMENT '销售单号',
    mone_ys DECIMAL(10,2) COMMENT '应收金额',
    mone_sk DECIMAL(10,2) COMMENT '收款金额',
    mone_ye DECIMAL(10,2) COMMENT '余额',
    note TEXT COMMENT '备注',
    flag_qr CHAR(2) COMMENT '确认标志',
    oper_qr CHAR(10) COMMENT '确认操作员',
    date_qr DATE COMMENT '确认日期',
    PRIMARY KEY (zth, sheetid_xs)
) COMMENT='期初应收款表';

-- ========== 单据表 ==========
-- 采购单主表
CREATE TABLE jx_sheet_cg_main (
    zth CHAR(2) NOT NULL COMMENT '账套号',
    sheetid CHAR(13) NOT NULL COMMENT '单据号',
    sheetdate DATE COMMENT '单据日期',
    oper_code CHAR(10) COMMENT '操作员代码',
    mone DECIMAL(10,2) COMMENT '金额',
    tax DECIMAL(10,2) COMMENT '税额',
    supp_code CHAR(10) COMMENT '供应商代码',
    flag_qr CHAR(2) COMMENT '确认标志',
    oper_qr CHAR(10) COMMENT '确认操作员',
    date_qr DATE COMMENT '确认日期',
    note TEXT COMMENT '备注',
    PRIMARY KEY (zth, sheetid),
    FOREIGN KEY (oper_code) REFERENCES jx_d_operator(oper_code),
    FOREIGN KEY (supp_code) REFERENCES jx_d_supp(supp_code)
) COMMENT='采购单主表';

-- 采购单明细表
CREATE TABLE jx_sheet_cg_item (
    zth CHAR(2) NOT NULL COMMENT '账套号',
    sheetid CHAR(13) NOT NULL COMMENT '单据号',
    itemno INTEGER NOT NULL COMMENT '明细行号',
    goods_code CHAR(13) COMMENT '商品代码',
    amount INTEGER COMMENT '数量',
    price DECIMAL(8,2) COMMENT '单价',
    mone DECIMAL(10,2) COMMENT '金额',
    tax DECIMAL(10,2) COMMENT '税额',
    note TEXT COMMENT '备注',
    PRIMARY KEY (zth, sheetid, itemno),
    FOREIGN KEY (zth, sheetid) REFERENCES jx_sheet_cg_main(zth, sheetid),
    FOREIGN KEY (goods_code) REFERENCES jx_d_goods(goods_code)
) COMMENT='采购单明细表';

-- 销售单主表
CREATE TABLE jx_sheet_xs_main (
    zth CHAR(2) NOT NULL COMMENT '账套号',
    sheetid CHAR(13) NOT NULL COMMENT '单据号',
    sheetdate DATE COMMENT '单据日期',
    oper_code CHAR(10) COMMENT '操作员代码',
    mone DECIMAL(10,2) COMMENT '金额',
    tax DECIMAL(10,2) COMMENT '税额',
    flag_qr CHAR(2) COMMENT '确认标志',
    oper_qr CHAR(10) COMMENT '确认操作员',
    date_qr DATE COMMENT '确认日期',
    note TEXT COMMENT '备注',
    PRIMARY KEY (zth, sheetid),
    FOREIGN KEY (oper_code) REFERENCES jx_d_operator(oper_code)
) COMMENT='销售单主表';

-- 销售单明细表
CREATE TABLE jx_sheet_xs_item (
    zth CHAR(2) NOT NULL COMMENT '账套号',
    sheetid CHAR(13) NOT NULL COMMENT '单据号',
    itemno INTEGER NOT NULL COMMENT '明细行号',
    goods_code CHAR(13) COMMENT '商品代码',
    amount INTEGER COMMENT '数量',
    price DECIMAL(8,2) COMMENT '单价',
    discount DECIMAL(5,3) COMMENT '折扣',
    mone DECIMAL(10,2) COMMENT '金额',
    tax DECIMAL(10,2) COMMENT '税额',
    note TEXT COMMENT '备注',
    PRIMARY KEY (zth, sheetid, itemno),
    FOREIGN KEY (zth, sheetid) REFERENCES jx_sheet_xs_main(zth, sheetid),
    FOREIGN KEY (goods_code) REFERENCES jx_d_goods(goods_code)
) COMMENT='销售单明细表';

-- 销售单成本明细表
CREATE TABLE jx_sheet_xs_item_cb (
    zth CHAR(2) NOT NULL COMMENT '账套号',
    sheetid CHAR(13) NOT NULL COMMENT '单据号',
    itemno INTEGER NOT NULL COMMENT '明细行号',
    goods_code CHAR(13) COMMENT '商品代码',
    amount INTEGER COMMENT '数量',
    price DECIMAL(8,2) COMMENT '成本单价',
    mone DECIMAL(10,2) COMMENT '成本金额',
    note TEXT COMMENT '备注',
    PRIMARY KEY (zth, sheetid, itemno),
    FOREIGN KEY (zth, sheetid) REFERENCES jx_sheet_xs_main(zth, sheetid),
    FOREIGN KEY (goods_code) REFERENCES jx_d_goods(goods_code)
) COMMENT='销售单成本明细表';

-- 报损单主表
CREATE TABLE jx_sheet_bs_main (
    zth CHAR(2) NOT NULL COMMENT '账套号',
    sheetid CHAR(13) NOT NULL COMMENT '单据号',
    sheetdate DATE COMMENT '单据日期',
    oper_code CHAR(10) COMMENT '操作员代码',
    mone DECIMAL(10,2) COMMENT '金额',
    flag_qr CHAR(2) COMMENT '确认标志',
    oper_qr CHAR(10) COMMENT '确认操作员',
    date_qr DATE COMMENT '确认日期',
    note TEXT COMMENT '备注',
    PRIMARY KEY (zth, sheetid),
    FOREIGN KEY (oper_code) REFERENCES jx_d_operator(oper_code)
) COMMENT='报损单主表';

-- 报损单明细表
CREATE TABLE jx_sheet_bs_item (
    zth CHAR(2) NOT NULL COMMENT '账套号',
    sheetid CHAR(13) NOT NULL COMMENT '单据号',
    itemno INTEGER NOT NULL COMMENT '明细行号',
    goods_code CHAR(13) COMMENT '商品代码',
    amount INTEGER COMMENT '数量',
    price DECIMAL(8,2) COMMENT '单价',
    mone DECIMAL(10,2) COMMENT '金额',
    note TEXT COMMENT '备注',
    PRIMARY KEY (zth, sheetid, itemno),
    FOREIGN KEY (zth, sheetid) REFERENCES jx_sheet_bs_main(zth, sheetid),
    FOREIGN KEY (goods_code) REFERENCES jx_d_goods(goods_code)
) COMMENT='报损单明细表';

-- 盘存单主表
CREATE TABLE jx_sheet_pc_main (
    zth CHAR(2) NOT NULL COMMENT '账套号',
    sheetid CHAR(13) NOT NULL COMMENT '单据号',
    sheetdate DATE COMMENT '单据日期',
    oper_code CHAR(10) COMMENT '操作员代码',
    flag_qr CHAR(2) COMMENT '确认标志',
    oper_qr CHAR(10) COMMENT '确认操作员',
    date_qr DATE COMMENT '确认日期',
    note TEXT COMMENT '备注',
    PRIMARY KEY (zth, sheetid),
    FOREIGN KEY (oper_code) REFERENCES jx_d_operator(oper_code)
) COMMENT='盘存单主表';

-- 盘存单明细表
CREATE TABLE jx_sheet_pc_item (
    zth CHAR(2) NOT NULL COMMENT '账套号',
    sheetid CHAR(13) NOT NULL COMMENT '单据号',
    itemno INTEGER NOT NULL COMMENT '明细行号',
    goods_code CHAR(13) COMMENT '商品代码',
    amount_zm INTEGER COMMENT '账面数量',
    amount_sp INTEGER COMMENT '实盘数量',
    amount_yk INTEGER COMMENT '盈亏数量',
    note TEXT COMMENT '备注',
    PRIMARY KEY (zth, sheetid, itemno),
    FOREIGN KEY (zth, sheetid) REFERENCES jx_sheet_pc_main(zth, sheetid),
    FOREIGN KEY (goods_code) REFERENCES jx_d_goods(goods_code)
) COMMENT='盘存单明细表';

-- 付款单
CREATE TABLE jx_sheet_fk (
    zth CHAR(2) NOT NULL COMMENT '账套号',
    sheetid CHAR(13) NOT NULL COMMENT '单据号',
    sheetdate DATE COMMENT '单据日期',
    oper_code CHAR(10) COMMENT '操作员代码',
    mone DECIMAL(10,2) COMMENT '金额',
    supp_code CHAR(10) COMMENT '供应商代码',
    flag_qr CHAR(2) COMMENT '确认标志',
    oper_qr CHAR(10) COMMENT '确认操作员',
    date_qr DATE COMMENT '确认日期',
    note TEXT COMMENT '备注',
    PRIMARY KEY (zth, sheetid),
    FOREIGN KEY (oper_code) REFERENCES jx_d_operator(oper_code),
    FOREIGN KEY (supp_code) REFERENCES jx_d_supp(supp_code)
) COMMENT='付款单';

-- 收款单
CREATE TABLE jx_sheet_sk (
    zth CHAR(2) NOT NULL COMMENT '账套号',
    sheetid CHAR(13) NOT NULL COMMENT '单据号',
    sheetdate DATE COMMENT '单据日期',
    oper_code CHAR(10) COMMENT '操作员代码',
    sheetid_xs CHAR(13) COMMENT '销售单号',
    mone DECIMAL(10,2) COMMENT '金额',
    flag_qr CHAR(2) COMMENT '确认标志',
    oper_qr CHAR(10) COMMENT '确认操作员',
    date_qr DATE COMMENT '确认日期',
    note TEXT COMMENT '备注',
    PRIMARY KEY (zth, sheetid),
    FOREIGN KEY (oper_code) REFERENCES jx_d_operator(oper_code)
) COMMENT='收款单';

-- ========== 账表 ==========
-- 商品库存表
CREATE TABLE jx_table_goods_amount (
    zth CHAR(2) NOT NULL COMMENT '账套号',
    goods_code CHAR(13) NOT NULL COMMENT '商品代码',
    amount INTEGER COMMENT '库存数量',
    PRIMARY KEY (zth, goods_code),
    FOREIGN KEY (goods_code) REFERENCES jx_d_goods(goods_code)
) COMMENT='商品库存表';

-- 应付账表
CREATE TABLE jx_table_yf_mone (
    zth CHAR(2) NOT NULL COMMENT '账套号',
    supp_code CHAR(10) NOT NULL COMMENT '供应商代码',
    itemno INTEGER NOT NULL COMMENT '明细行号',
    sheetdate DATE COMMENT '单据日期',
    mone_yf DECIMAL(10,2) COMMENT '应付金额',
    mone_fk DECIMAL(10,2) COMMENT '付款金额',
    mone_ye DECIMAL(10,2) COMMENT '余额',
    sheettype VARCHAR(10) COMMENT '单据类型',
    sheetid CHAR(13) COMMENT '单据号',
    note TEXT COMMENT '备注',
    PRIMARY KEY (zth, supp_code, itemno),
    FOREIGN KEY (supp_code) REFERENCES jx_d_supp(supp_code)
) COMMENT='应付账表';

-- 应收账表
CREATE TABLE jx_table_ys_mone (
    zth CHAR(2) NOT NULL COMMENT '账套号',
    sheetid_xs CHAR(13) NOT NULL COMMENT '销售单号',
    itemno INTEGER NOT NULL COMMENT '明细行号',
    sheetdate DATE COMMENT '单据日期',
    mone_ys DECIMAL(10,2) COMMENT '应收金额',
    mone_sk DECIMAL(10,2) COMMENT '收款金额',
    mone_ye DECIMAL(10,2) COMMENT '余额',
    sheetid_sk CHAR(13) COMMENT '收款单号',
    note TEXT COMMENT '备注',
    PRIMARY KEY (zth, sheetid_xs, itemno)
) COMMENT='应收账表';

-- 进销存数量报表
CREATE TABLE jx_report_amount (
    zth CHAR(2) NOT NULL COMMENT '账套号',
    date_min DATE NOT NULL COMMENT '开始日期',
    date_max DATE COMMENT '结束日期',
    goods_code CHAR(13) NOT NULL COMMENT '商品代码',
    amount_qc INTEGER COMMENT '期初数量',
    amount_cg INTEGER COMMENT '采购数量',
    amount_xs INTEGER COMMENT '销售数量',
    amount_bs INTEGER COMMENT '报损数量',
    amount_qm INTEGER COMMENT '期末数量',
    amount_yk INTEGER COMMENT '盈亏数量',
    amount_sp INTEGER COMMENT '实盘数量',
    note TEXT COMMENT '备注',
    PRIMARY KEY (zth, date_min, goods_code),
    FOREIGN KEY (goods_code) REFERENCES jx_d_goods(goods_code)
) COMMENT='进销存数量报表';

-- 流水账表
CREATE TABLE jx_table_goods_record (
    zth CHAR(2) NOT NULL COMMENT '账套号',
    sheetdate DATE NOT NULL COMMENT '单据日期',
    goods_code CHAR(13) NOT NULL COMMENT '商品代码',
    itemno INTEGER NOT NULL COMMENT '明细行号',
    sheettype VARCHAR(10) COMMENT '单据类型',
    sheetid CHAR(13) COMMENT '单据号',
    amount_add INTEGER COMMENT '增加数量',
    amount_sub INTEGER COMMENT '减少数量',
    price DECIMAL(8,2) COMMENT '单价',
    discount DECIMAL(5,3) COMMENT '折扣',
    PRIMARY KEY (zth, sheetdate, goods_code, itemno),
    FOREIGN KEY (goods_code) REFERENCES jx_d_goods(goods_code)
) COMMENT='流水账表';

-- 商品成本单价表
CREATE TABLE jx_table_goods_price (
    zth CHAR(2) NOT NULL COMMENT '账套号',
    date_min DATE NOT NULL COMMENT '开始日期',
    date_max DATE NOT NULL COMMENT '结束日期',
    goods_code CHAR(13) NOT NULL COMMENT '商品代码',
    price1 DECIMAL(10,2) COMMENT '月末一次加权平均单价',
    price2 DECIMAL(10,2) COMMENT '移动加权平均单价',
    price3 DECIMAL(10,2) COMMENT '先进先出单价',
    PRIMARY KEY (zth, goods_code, date_min),
    FOREIGN KEY (goods_code) REFERENCES jx_d_goods(goods_code)
) COMMENT='商品成本单价表';

-- 移动加权平均法、先进先出法成本计算表
CREATE TABLE jx_table_goods_price23 (
    zth CHAR(2) NOT NULL COMMENT '账套号',
    goods_code CHAR(13) NOT NULL COMMENT '商品代码',
    itemno INTEGER NOT NULL COMMENT '明细行号',
    sheet_type VARCHAR(10) COMMENT '单据类型',
    sheetid CHAR(13) COMMENT '单据号',
    sheetdate DATE COMMENT '单据日期',
    amount INTEGER COMMENT '数量',
    price DECIMAL(8,2) COMMENT '单价',
    mone DECIMAL(10,2) COMMENT '金额',
    sheet_kc INTEGER COMMENT '库存数量',
    amount_kc INTEGER COMMENT '库存数量',
    mone_kc2 DECIMAL(10,2) COMMENT '移动加权平均库存金额',
    mone_sheet_xs DECIMAL(10,2) COMMENT '销售金额',
    mone_kc3 DECIMAL(10,2) COMMENT '先进先出库存金额',
    price2 DECIMAL(8,2) COMMENT '移动加权平均单价',
    price3 DECIMAL(8,2) COMMENT '先进先出单价',
    price3_kc DECIMAL(8,2) COMMENT '先进先出库存单价',
    PRIMARY KEY (zth, goods_code, itemno),
    FOREIGN KEY (goods_code) REFERENCES jx_d_goods(goods_code)
) COMMENT='移动加权平均法、先进先出法成本计算表';

-- 月末一次加权平均法、移动加权平均法、先进先出法库存成本表
CREATE TABLE jx_table_goods_price123 (
    zth CHAR(2) NOT NULL COMMENT '账套号',
    date_min DATE NOT NULL COMMENT '开始日期',
    date_max DATE COMMENT '结束日期',
    goods_code CHAR(13) NOT NULL COMMENT '商品代码',
    amount_ini INTEGER COMMENT '期初数量',
    amount_buy INTEGER COMMENT '采购数量',
    amount_sale INTEGER COMMENT '销售数量',
    amount_end INTEGER COMMENT '期末数量',
    mone_ini1 DECIMAL(10,2) COMMENT '期初金额1',
    mone_buy DECIMAL(10,2) COMMENT '采购金额',
    mone_end1 DECIMAL(10,2) COMMENT '期末金额1',
    price1 DECIMAL(10,2) COMMENT '月末一次加权平均单价',
    mone_end2 DECIMAL(10,2) COMMENT '期末金额2',
    price2 DECIMAL(10,2) COMMENT '移动加权平均单价',
    mone_end3 DECIMAL(10,2) COMMENT '期末金额3',
    price3 DECIMAL(10,2) COMMENT '先进先出单价',
    PRIMARY KEY (zth, date_min, goods_code),
    FOREIGN KEY (goods_code) REFERENCES jx_d_goods(goods_code)
) COMMENT='月末一次加权平均法、移动加权平均法、先进先出法库存成本表';

-- ========== 会计系统表 ==========
-- 科目类别码表
CREATE TABLE zw_c_kmlb (
    kmlb_code CHAR(2) NOT NULL COMMENT '科目类别代码',
    kmlb_name VARCHAR(10) COMMENT '科目类别名称',
    PRIMARY KEY (kmlb_code)
) COMMENT='科目类别码表';

-- 科目性质码表
CREATE TABLE zw_c_kmxz (
    kmxz_code CHAR(2) NOT NULL COMMENT '科目性质代码',
    kmxz_name VARCHAR(10) COMMENT '科目性质名称',
    PRIMARY KEY (kmxz_code)
) COMMENT='科目性质码表';

-- 币别码表
CREATE TABLE zw_c_bb (
    bb_code CHAR(2) NOT NULL COMMENT '币别代码',
    bb_name VARCHAR(10) COMMENT '币别名称',
    PRIMARY KEY (bb_code)
) COMMENT='币别码表';

-- 行业码表
CREATE TABLE zw_c_hy (
    hy_code CHAR(10) NOT NULL COMMENT '行业代码',
    hy_name VARCHAR(50) COMMENT '行业名称',
    PRIMARY KEY (hy_code)
) COMMENT='行业码表';

-- 财务指标码表
CREATE TABLE zw_c_cwzbmb (
    cwzb_code CHAR(10) NOT NULL COMMENT '财务指标代码',
    cwzb_name VARCHAR(30) COMMENT '财务指标名称',
    cwzb_dy VARCHAR(260) COMMENT '财务指标定义',
    cwzb_jsgs VARCHAR(100) COMMENT '财务指标计算公式',
    PRIMARY KEY (cwzb_code)
) COMMENT='财务指标码表';

-- 账套信息表
CREATE TABLE zw_d_ztxxb (
    zth CHAR(2) NOT NULL COMMENT '账套号',
    ztmc VARCHAR(30) COMMENT '账套名称',
    hy_code CHAR(10) COMMENT '行业代码',
    bb_code CHAR(2) COMMENT '币别代码',
    qjs INTEGER COMMENT '启用期数',
    qsrq DATE COMMENT '启用日期',
    jsrq DATE COMMENT '结束日期',
    qyrq DATE COMMENT '启用日期',
    nkjqj VARCHAR(10) COMMENT '年会计期间',
    zt VARCHAR(10) COMMENT '状态',
    PRIMARY KEY (zth),
    FOREIGN KEY (hy_code) REFERENCES zw_c_hy(hy_code),
    FOREIGN KEY (bb_code) REFERENCES zw_c_bb(bb_code)
) COMMENT='账套信息表';

-- 会计科目编码表
CREATE TABLE zw_d_kjkmbmb (
    km_code CHAR(10) NOT NULL COMMENT '科目代码',
    km_name VARCHAR(50) COMMENT '科目名称',
    kmlb_code CHAR(2) COMMENT '科目类别代码',
    kmxz_code CHAR(2) COMMENT '科目性质代码',
    yefx CHAR(2) COMMENT '余额方向',
    PRIMARY KEY (km_code),
    FOREIGN KEY (kmlb_code) REFERENCES zw_c_kmlb(kmlb_code),
    FOREIGN KEY (kmxz_code) REFERENCES zw_c_kmxz(kmxz_code)
) COMMENT='会计科目编码表';

-- 操作员表
CREATE TABLE zw_d_czy (
    czy_code CHAR(10) NOT NULL COMMENT '操作员代码',
    czy_name VARCHAR(20) COMMENT '操作员姓名',
    mm VARCHAR(10) COMMENT '密码',
    PRIMARY KEY (czy_code)
) COMMENT='操作员表';

-- 凭证主表
CREATE TABLE zw_pz_zb (
    zth CHAR(2) NOT NULL COMMENT '账套号',
    nkjqj VARCHAR(10) NOT NULL COMMENT '年会计期间',
    ykjqj VARCHAR(10) NOT NULL COMMENT '月会计期间',
    pzh CHAR(13) NOT NULL COMMENT '凭证号',
    rq DATE COMMENT '日期',
    fdjs INTEGER COMMENT '分录笔数',
    zdr CHAR(10) COMMENT '制单人',
    zdrq DATE COMMENT '制单日期',
    shr CHAR(10) COMMENT '审核人',
    shrq DATE COMMENT '审核日期',
    shbj CHAR(2) COMMENT '审核标记',
    jzr CHAR(10) COMMENT '记账人',
    jzrq DATE COMMENT '记账日期',
    jzbj CHAR(2) COMMENT '记账标记',
    bz TEXT COMMENT '备注',
    PRIMARY KEY (zth, nkjqj, ykjqj, pzh),
    FOREIGN KEY (zth) REFERENCES zw_d_ztxxb(zth)
) COMMENT='凭证主表';

-- 凭证明细表
CREATE TABLE zw_pz_mxb (
    zth CHAR(2) NOT NULL COMMENT '账套号',
    nkjqj VARCHAR(10) NOT NULL COMMENT '年会计期间',
    ykjqj VARCHAR(10) NOT NULL COMMENT '月会计期间',
    pzh CHAR(13) NOT NULL COMMENT '凭证号',
    km_code CHAR(10) NOT NULL COMMENT '科目代码',
    zy VARCHAR(100) COMMENT '摘要',
    jfje DECIMAL(12,2) COMMENT '借方金额',
    dfje DECIMAL(12,2) COMMENT '贷方金额',
    PRIMARY KEY (zth, nkjqj, ykjqj, pzh, km_code),
    FOREIGN KEY (zth, nkjqj, ykjqj, pzh) REFERENCES zw_pz_zb(zth, nkjqj, ykjqj, pzh),
    FOREIGN KEY (km_code) REFERENCES zw_d_kjkmbmb(km_code)
) COMMENT='凭证明细表';

-- 科目余额表
CREATE TABLE zw_zb_kmyeb (
    zth CHAR(2) NOT NULL COMMENT '账套号',
    nkjqj VARCHAR(10) NOT NULL COMMENT '年会计期间',
    ykjqj VARCHAR(10) NOT NULL COMMENT '月会计期间',
    km_code CHAR(10) NOT NULL COMMENT '科目代码',
    qcjfye DECIMAL(12,2) COMMENT '期初借方余额',
    qcdfye DECIMAL(12,2) COMMENT '期初贷方余额',
    qcye DECIMAL(12,2) COMMENT '期初余额',
    jffse DECIMAL(12,2) COMMENT '借方发生额',
    dffse DECIMAL(12,2) COMMENT '贷方发生额',
    jflj DECIMAL(12,2) COMMENT '借方累计',
    dflj DECIMAL(12,2) COMMENT '贷方累计',
    qmjfye DECIMAL(12,2) COMMENT '期末借方余额',
    qmdfye DECIMAL(12,2) COMMENT '期末贷方余额',
    qmye DECIMAL(12,2) COMMENT '期末余额',
    PRIMARY KEY (zth, km_code, nkjqj, ykjqj),
    FOREIGN KEY (zth) REFERENCES zw_d_ztxxb(zth),
    FOREIGN KEY (km_code) REFERENCES zw_d_kjkmbmb(km_code)
) COMMENT='科目余额表';

-- 明细账表
CREATE TABLE zw_zb_mxzb (
    zth CHAR(2) NOT NULL COMMENT '账套号',
    nkjqj VARCHAR(10) NOT NULL COMMENT '年会计期间',
    ykjqj VARCHAR(10) NOT NULL COMMENT '月会计期间',
    km_code CHAR(10) NOT NULL COMMENT '科目代码',
    xh INTEGER NOT NULL COMMENT '序号',
    rq DATE COMMENT '日期',
    pzh CHAR(13) COMMENT '凭证号',
    zy VARCHAR(100) COMMENT '摘要',
    jfje DECIMAL(12,2) COMMENT '借方金额',
    dfje DECIMAL(12,2) COMMENT '贷方金额',
    PRIMARY KEY (zth, nkjqj, ykjqj, km_code, xh),
    FOREIGN KEY (zth) REFERENCES zw_d_ztxxb(zth),
    FOREIGN KEY (km_code) REFERENCES zw_d_kjkmbmb(km_code)
) COMMENT='明细账表';

-- 资产负债表
CREATE TABLE zw_yb_zcfzb (
    zth CHAR(2) NOT NULL COMMENT '账套号',
    nkjqj VARCHAR(10) NOT NULL COMMENT '年会计期间',
    ykjqj VARCHAR(10) NOT NULL COMMENT '月会计期间',
    hbzjqcye DECIMAL(12,2) COMMENT '货币资金期初余额',
    hbzjqmye DECIMAL(12,2) COMMENT '货币资金期末余额',
    gyjzjljrzcqcye DECIMAL(12,2) COMMENT '固定资产累计折旧期初余额',
    gyjzjljrzcqmye DECIMAL(12,2) COMMENT '固定资产累计折旧期末余额',
    yspjjyszkqcye DECIMAL(12,2) COMMENT '应收票据及应收账款期初余额',
    yspjjyszkqmye DECIMAL(12,2) COMMENT '应收票据及应收账款期末余额',
    yfkxqcye DECIMAL(12,2) COMMENT '预付款项期初余额',
    yfkxqmye DECIMAL(12,2) COMMENT '预付款项期末余额',
    qtyskqcye DECIMAL(12,2) COMMENT '其他应收款期初余额',
    qtyskqmye DECIMAL(12,2) COMMENT '其他应收款期末余额',
    chqcye DECIMAL(12,2) COMMENT '存货期初余额',
    chqmye DECIMAL(12,2) COMMENT '存货期末余额',
    ldzchqcye DECIMAL(12,2) COMMENT '流动资产合计期初余额',
    ldzchqmye DECIMAL(12,2) COMMENT '流动资产合计期末余额',
    cyzqtqcye DECIMAL(12,2) COMMENT '长期资产其他期初余额',
    cyzqtqmye DECIMAL(12,2) COMMENT '长期资产其他期末余额',
    cqyskqcye DECIMAL(12,2) COMMENT '长期应收款期初余额',
    cqyskqmye DECIMAL(12,2) COMMENT '长期应收款期末余额',
    cqgqtzqcye DECIMAL(12,2) COMMENT '长期股权投资期初余额',
    cqgqtzqmye DECIMAL(12,2) COMMENT '长期股权投资期末余额',
    tzxfdcqcye DECIMAL(12,2) COMMENT '投资性房地产期初余额',
    tzxfdcqmye DECIMAL(12,2) COMMENT '投资性房地产期末余额',
    gdzcqcye DECIMAL(12,2) COMMENT '固定资产期初余额',
    gdzcqmye DECIMAL(12,2) COMMENT '固定资产期末余额',
    zjgcqcye DECIMAL(12,2) COMMENT '在建工程期初余额',
    zjgcqmye DECIMAL(12,2) COMMENT '在建工程期末余额',
    wxzcqcye DECIMAL(12,2) COMMENT '无形资产期初余额',
    wxzcqmye DECIMAL(12,2) COMMENT '无形资产期末余额',
    kfzcqcye DECIMAL(12,2) COMMENT '开发支出期初余额',
    kfzcqmye DECIMAL(12,2) COMMENT '开发支出期末余额',
    cqdtfyqcye DECIMAL(12,2) COMMENT '长期待摊费用期初余额',
    cqdtfyqmye DECIMAL(12,2) COMMENT '长期待摊费用期末余额',
    dysdszcqcye DECIMAL(12,2) COMMENT '递延所得税资产期初余额',
    dysdszcqmye DECIMAL(12,2) COMMENT '递延所得税资产期末余额',
    fldzchqcye DECIMAL(12,2) COMMENT '非流动资产合计期初余额',
    fldzchqmye DECIMAL(12,2) COMMENT '非流动资产合计期末余额',
    zczjqcye DECIMAL(12,2) COMMENT '资产总计期初余额',
    zczjqmye DECIMAL(12,2) COMMENT '资产总计期末余额',
    dqjkqcye DECIMAL(12,2) COMMENT '短期借款期初余额',
    dqjkqmye DECIMAL(12,2) COMMENT '短期借款期末余额',
    yfpjjyfzkqcye DECIMAL(12,2) COMMENT '应付票据及应付账款期初余额',
    yfpjjyfzkqmye DECIMAL(12,2) COMMENT '应付票据及应付账款期末余额',
    yskxqcye DECIMAL(12,2) COMMENT '预收款项期初余额',
    yskxqmye DECIMAL(12,2) COMMENT '预收款项期末余额',
    yfzgxcqcye DECIMAL(12,2) COMMENT '应付职工薪酬期初余额',
    yfzgxcqmye DECIMAL(12,2) COMMENT '应付职工薪酬期末余额',
    yjsfqcye DECIMAL(12,2) COMMENT '应交税费期初余额',
    yjsfqmye DECIMAL(12,2) COMMENT '应交税费期末余额',
    qtysfkqcye DECIMAL(12,2) COMMENT '其他应付款期初余额',
    qtysfkqmye DECIMAL(12,2) COMMENT '其他应付款期末余额',
    ldfzhqcye DECIMAL(12,2) COMMENT '流动负债合计期初余额',
    ldfzhqmye DECIMAL(12,2) COMMENT '流动负债合计期末余额',
    cqjkqcye DECIMAL(12,2) COMMENT '长期借款期初余额',
    cqjkqmye DECIMAL(12,2) COMMENT '长期借款期末余额',
    yfzqqcye DECIMAL(12,2) COMMENT '应付债券期初余额',
    yfzqqmye DECIMAL(12,2) COMMENT '应付债券期末余额',
    cqyfkqcye DECIMAL(12,2) COMMENT '长期应付款期初余额',
    cqyfkqmye DECIMAL(12,2) COMMENT '长期应付款期末余额',
    dysdsqcye DECIMAL(12,2) COMMENT '递延所得税负债期初余额',
    dysdsqmye DECIMAL(12,2) COMMENT '递延所得税负债期末余额',
    fldfzhqcye DECIMAL(12,2) COMMENT '非流动负债合计期初余额',
    fldfzhqmye DECIMAL(12,2) COMMENT '非流动负债合计期末余额',
    fzhqcye DECIMAL(12,2) COMMENT '负债合计期初余额',
    fzhqmye DECIMAL(12,2) COMMENT '负债合计期末余额',
    sszqcye DECIMAL(12,2) COMMENT '实收资本期初余额',
    sszqmye DECIMAL(12,2) COMMENT '实收资本期末余额',
    zbgjqcye DECIMAL(12,2) COMMENT '资本公积期初余额',
    zbgjqmye DECIMAL(12,2) COMMENT '资本公积期末余额',
    kcgqcye DECIMAL(12,2) COMMENT '库存股期初余额',
    kcgqmye DECIMAL(12,2) COMMENT '库存股期末余额',
    qtzshsyqcye DECIMAL(12,2) COMMENT '其他综合收益期初余额',
    qtzshsyqmye DECIMAL(12,2) COMMENT '其他综合收益期末余额',
    yygqcye DECIMAL(12,2) COMMENT '盈余公积期初余额',
    yygqmye DECIMAL(12,2) COMMENT '盈余公积期末余额',
    wfplrqcye DECIMAL(12,2) COMMENT '未分配利润期初余额',
    wfplrqmye DECIMAL(12,2) COMMENT '未分配利润期末余额',
    syzqyhqcye DECIMAL(12,2) COMMENT '所有者权益合计期初余额',
    syzqyhqmye DECIMAL(12,2) COMMENT '所有者权益合计期末余额',
    fzhsyzqyhqcye DECIMAL(12,2) COMMENT '负债和所有者权益合计期初余额',
    fzhsyzqyhqmye DECIMAL(12,2) COMMENT '负债和所有者权益合计期末余额',
    PRIMARY KEY (zth, nkjqj, ykjqj),
    FOREIGN KEY (zth) REFERENCES zw_d_ztxxb(zth)
) COMMENT='资产负债表';

-- 利润表
CREATE TABLE zw_yb_lrb (
    zth CHAR(2) NOT NULL COMMENT '账套号',
    nkjqj VARCHAR(10) NOT NULL COMMENT '年会计期间',
    ykjqj VARCHAR(10) NOT NULL COMMENT '月会计期间',
    yysrsqje DECIMAL(12,2) COMMENT '营业收入本期金额',
    yysrbqje DECIMAL(12,2) COMMENT '营业收入本期金额',
    yycbsqje DECIMAL(12,2) COMMENT '营业成本本期金额',
    yycbbqje DECIMAL(12,2) COMMENT '营业成本本期金额',
    sjjfjsqje DECIMAL(12,2) COMMENT '税金及附加本期金额',
    sjjfjbqje DECIMAL(12,2) COMMENT '税金及附加本期金额',
    xsfysqje DECIMAL(12,2) COMMENT '销售费用本期金额',
    xsfybqje DECIMAL(12,2) COMMENT '销售费用本期金额',
    glfysqje DECIMAL(12,2) COMMENT '管理费用本期金额',
    glfybqje DECIMAL(12,2) COMMENT '管理费用本期金额',
    yffysqje DECIMAL(12,2) COMMENT '研发费用本期金额',
    yffybqje DECIMAL(12,2) COMMENT '研发费用本期金额',
    cwfysqje DECIMAL(12,2) COMMENT '财务费用本期金额',
    cwfybqje DECIMAL(12,2) COMMENT '财务费用本期金额',
    lxfysqje DECIMAL(12,2) COMMENT '利息费用本期金额',
    lxfybqje DECIMAL(12,2) COMMENT '利息费用本期金额',
    lxsrsqje DECIMAL(12,2) COMMENT '利息收入本期金额',
    lxsrbqje DECIMAL(12,2) COMMENT '利息收入本期金额',
    zcjzssqje DECIMAL(12,2) COMMENT '资产减值损失本期金额',
    zcjzssbqje DECIMAL(12,2) COMMENT '资产减值损失本期金额',
    qtsysqje DECIMAL(12,2) COMMENT '其他收益本期金额',
    qtsybqje DECIMAL(12,2) COMMENT '其他收益本期金额',
    tzsysqje DECIMAL(12,2) COMMENT '投资收益本期金额',
    tzsybqje DECIMAL(12,2) COMMENT '投资收益本期金额',
    gyjzbdsysqje DECIMAL(12,2) COMMENT '固定资产变动收益本期金额',
    gyjzbdsybqje DECIMAL(12,2) COMMENT '固定资产变动收益本期金额',
    zcczsysqje DECIMAL(12,2) COMMENT '资产处置收益本期金额',
    zcczsybqje DECIMAL(12,2) COMMENT '资产处置收益本期金额',
    yylrsqje DECIMAL(12,2) COMMENT '营业利润本期金额',
    yylrbqje DECIMAL(12,2) COMMENT '营业利润本期金额',
    yywsrsqje DECIMAL(12,2) COMMENT '营业外收入本期金额',
    yywsrbqje DECIMAL(12,2) COMMENT '营业外收入本期金额',
    yywzcsqje DECIMAL(12,2) COMMENT '营业外支出本期金额',
    yywzcbqje DECIMAL(12,2) COMMENT '营业外支出本期金额',
    lrzesqje DECIMAL(12,2) COMMENT '利润总额本期金额',
    lrzebqje DECIMAL(12,2) COMMENT '利润总额本期金额',
    sdsfsqje DECIMAL(12,2) COMMENT '所得税费用本期金额',
    sdsfybqje DECIMAL(12,2) COMMENT '所得税费用本期金额',
    jlrsqje DECIMAL(12,2) COMMENT '净利润本期金额',
    jlrbqje DECIMAL(12,2) COMMENT '净利润本期金额',
    qtzshsydshjsqje DECIMAL(12,2) COMMENT '其他综合收益的税后净额本期金额',
    qtzshsydshjbqje DECIMAL(12,2) COMMENT '其他综合收益的税后净额本期金额',
    zhsyzsqje DECIMAL(12,2) COMMENT '综合收益总额本期金额',
    zhsyzbqje DECIMAL(12,2) COMMENT '综合收益总额本期金额',
    mgsysqje DECIMAL(12,2) COMMENT '每股收益本期金额',
    mgsybqje DECIMAL(12,2) COMMENT '每股收益本期金额',
    PRIMARY KEY (zth, nkjqj, ykjqj),
    FOREIGN KEY (zth) REFERENCES zw_d_ztxxb(zth)
) COMMENT='利润表';

-- 财务指标统计表
CREATE TABLE zw_yb_cwzbtjb (
    zth CHAR(2) NOT NULL COMMENT '账套号',
    nkjqj VARCHAR(10) NOT NULL COMMENT '年会计期间',
    ykjqj VARCHAR(10) NOT NULL COMMENT '月会计期间',
    cwzb_code CHAR(10) NOT NULL COMMENT '财务指标代码',
    cwzb_jsjg DECIMAL(6,4) COMMENT '财务指标计算结果',
    bz VARCHAR(100) COMMENT '备注',
    PRIMARY KEY (zth, nkjqj, ykjqj, cwzb_code),
    FOREIGN KEY (zth) REFERENCES zw_d_ztxxb(zth),
    FOREIGN KEY (cwzb_code) REFERENCES zw_c_cwzbmb(cwzb_code)
) COMMENT='财务指标统计表';

-- 杜邦分析表
CREATE TABLE zw_yb_dbfxb (
    zth CHAR(2) NOT NULL COMMENT '账套号',
    nkjqj VARCHAR(10) NOT NULL COMMENT '年会计期间',
    ykjqj VARCHAR(10) NOT NULL COMMENT '月会计期间',
    jzcsyl DECIMAL(12,4) COMMENT '净资产收益率',
    tzbcl DECIMAL(12,4) COMMENT '投资报酬率',
    pjqycs DECIMAL(12,4) COMMENT '平均企业成本',
    yyjll DECIMAL(12,4) COMMENT '营业净利率',
    zzczzl DECIMAL(12,4) COMMENT '总资产周转率',
    jlr DECIMAL(12,2) COMMENT '净利润',
    yysr DECIMAL(12,2) COMMENT '营业收入',
    zcpjze DECIMAL(12,2) COMMENT '资产平均总额',
    yylr DECIMAL(12,2) COMMENT '营业利润',
    yywsr DECIMAL(12,2) COMMENT '营业外收入',
    yywzc DECIMAL(12,2) COMMENT '营业外支出',
    sdsfyy DECIMAL(12,2) COMMENT '所得税费用',
    yycb DECIMAL(12,2) COMMENT '营业成本',
    xsfy DECIMAL(12,2) COMMENT '销售费用',
    glfy DECIMAL(12,2) COMMENT '管理费用',
    cwfy DECIMAL(12,2) COMMENT '财务费用',
    zcjzss DECIMAL(12,2) COMMENT '资产减值损失',
    PRIMARY KEY (zth, nkjqj, ykjqj),
    FOREIGN KEY (zth) REFERENCES zw_d_ztxxb(zth)
) COMMENT='杜邦分析表';
