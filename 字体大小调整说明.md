# 登录界面字体大小调整说明

## 当前字体设置

### 主要元素字体大小
- **标题 "欢迎登录"**: 22px (之前28px)
- **副标题 "JX进销存管理信息系统"**: 13px (之前16px)
- **标签文字 (用户名/密码)**: 13px (之前16px)
- **输入框文字**: 14px (之前16px)
- **按钮文字**: 14px (之前16px)
- **复选框文字**: 12px (之前14px)
- **忘记密码链接**: 12px (之前14px)
- **错误提示**: 12px (之前14px)

### 窗口尺寸调整
- **窗口大小**: 450x380 (之前520x450)
- **内边距**: 40x30 (之前50x40)
- **元素间距**: 减少了各种间距

### 控件尺寸调整
- **输入框高度**: 减少内边距到10x12px
- **按钮高度**: 登录按钮38px，取消按钮35px
- **圆角半径**: 统一调整为8px

## 如果还需要进一步调整

### 方案1: 更小的字体 (适合高分辨率屏幕)
```css
/* 标题 */
font-size: 20px;

/* 副标题 */
font-size: 12px;

/* 标签和按钮 */
font-size: 13px;

/* 输入框 */
font-size: 13px;

/* 小元素 */
font-size: 11px;
```

### 方案2: 更紧凑的布局
```css
/* 窗口大小 */
setFixedSize(420, 350)

/* 内边距 */
setContentsMargins(35, 25, 35, 25)

/* 间距 */
setSpacing(12)
```

### 方案3: 简化界面元素
- 移除副标题
- 减少按钮数量
- 简化错误提示样式

## 自定义调整方法

### 1. 修改字体大小
在 `ui/login_dialog.py` 文件中找到对应的样式设置：

```python
# 修改标题字体
QLabel#title {
    font-size: 18px;  # 改为更小的值
}

# 修改输入框字体
QLineEdit {
    font-size: 12px;  # 改为更小的值
}
```

### 2. 修改窗口大小
```python
self.setFixedSize(400, 320)  # 改为更小的尺寸
```

### 3. 修改间距
```python
main_layout.setSpacing(12)  # 减少间距
main_layout.setContentsMargins(30, 20, 30, 20)  # 减少边距
```

## 推荐的最终设置

基于常见的界面设计标准，推荐以下设置：

### 字体大小
- 主标题: 18-20px
- 副标题: 11-12px  
- 正文标签: 12-13px
- 输入框: 13-14px
- 按钮: 13-14px
- 辅助文字: 10-11px

### 窗口尺寸
- 宽度: 400-450px
- 高度: 320-380px

### 间距设置
- 主要间距: 12-15px
- 次要间距: 5-8px
- 边距: 25-35px

## 测试不同设置

您可以通过修改以下参数来测试不同的效果：

1. **快速测试小字体**:
   - 将所有字体大小减少2-3px
   - 将窗口大小减少50px宽度和70px高度

2. **快速测试紧凑布局**:
   - 将所有间距减少3-5px
   - 将边距减少10-15px

3. **快速测试简化界面**:
   - 移除副标题
   - 合并某些元素

## 当前修改总结

已经对登录界面进行了以下优化：

✅ 减小了所有字体大小
✅ 缩小了窗口尺寸  
✅ 减少了元素间距
✅ 调整了控件大小
✅ 保持了界面美观性

如果您觉得字体还是太大，请告诉我具体哪些元素需要调整，我可以进一步优化。
