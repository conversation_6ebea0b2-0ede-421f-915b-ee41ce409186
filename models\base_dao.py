# -*- coding: utf-8 -*-
"""
基础数据访问对象(DAO)
"""

import pymysql
from typing import List, Dict, Any, Optional
from config import db_config
from utils import logger

class BaseDAO:
    """基础DAO类，提供通用的数据库操作方法"""
    
    def __init__(self):
        self.connection = None
    
    def get_connection(self) -> Optional[pymysql.Connection]:
        """获取数据库连接"""
        try:
            if not self.connection or not self.connection.open:
                self.connection = db_config.get_connection()
            return self.connection
        except Exception as e:
            logger.error(f"获取数据库连接失败: {e}")
            return None
    
    def close_connection(self):
        """关闭数据库连接"""
        if self.connection and self.connection.open:
            self.connection.close()
    
    def execute_query(self, sql: str, params: tuple = None) -> List[Dict[str, Any]]:
        """执行查询SQL"""
        connection = self.get_connection()
        if not connection:
            return []
        
        try:
            with connection.cursor() as cursor:
                cursor.execute(sql, params)
                result = cursor.fetchall()
                return result if result else []
        except Exception as e:
            logger.error(f"执行查询失败: {e}, SQL: {sql}, 参数: {params}")
            return []
    
    def execute_update(self, sql: str, params: tuple = None) -> bool:
        """执行更新SQL（INSERT, UPDATE, DELETE）"""
        connection = self.get_connection()
        if not connection:
            return False
        
        try:
            with connection.cursor() as cursor:
                cursor.execute(sql, params)
                connection.commit()
                return True
        except Exception as e:
            logger.error(f"执行更新失败: {e}, SQL: {sql}, 参数: {params}")
            connection.rollback()
            return False
    
    def execute_batch_update(self, sql: str, params_list: List[tuple]) -> bool:
        """批量执行更新SQL"""
        connection = self.get_connection()
        if not connection:
            return False
        
        try:
            with connection.cursor() as cursor:
                cursor.executemany(sql, params_list)
                connection.commit()
                return True
        except Exception as e:
            logger.error(f"批量执行更新失败: {e}, SQL: {sql}")
            connection.rollback()
            return False
    
    def get_by_id(self, table_name: str, id_field: str, id_value: Any) -> Optional[Dict[str, Any]]:
        """根据ID查询单条记录"""
        sql = f"SELECT * FROM {table_name} WHERE {id_field} = %s"
        result = self.execute_query(sql, (id_value,))
        return result[0] if result else None
    
    def get_all(self, table_name: str, order_by: str = None) -> List[Dict[str, Any]]:
        """查询所有记录"""
        sql = f"SELECT * FROM {table_name}"
        if order_by:
            sql += f" ORDER BY {order_by}"
        return self.execute_query(sql)
    
    def insert(self, table_name: str, data: Dict[str, Any]) -> bool:
        """插入记录"""
        if not data:
            return False
        
        fields = list(data.keys())
        placeholders = ', '.join(['%s'] * len(fields))
        sql = f"INSERT INTO {table_name} ({', '.join(fields)}) VALUES ({placeholders})"
        params = tuple(data.values())
        
        return self.execute_update(sql, params)
    
    def update(self, table_name: str, data: Dict[str, Any], where_clause: str, where_params: tuple) -> bool:
        """更新记录"""
        if not data:
            return False
        
        set_clause = ', '.join([f"{field} = %s" for field in data.keys()])
        sql = f"UPDATE {table_name} SET {set_clause} WHERE {where_clause}"
        params = tuple(data.values()) + where_params
        
        return self.execute_update(sql, params)
    
    def delete(self, table_name: str, where_clause: str, where_params: tuple) -> bool:
        """删除记录"""
        sql = f"DELETE FROM {table_name} WHERE {where_clause}"
        return self.execute_update(sql, where_params)
    
    def exists(self, table_name: str, where_clause: str, where_params: tuple) -> bool:
        """检查记录是否存在"""
        sql = f"SELECT 1 FROM {table_name} WHERE {where_clause} LIMIT 1"
        result = self.execute_query(sql, where_params)
        return len(result) > 0
    
    def count(self, table_name: str, where_clause: str = None, where_params: tuple = None) -> int:
        """统计记录数量"""
        sql = f"SELECT COUNT(*) as count FROM {table_name}"
        if where_clause:
            sql += f" WHERE {where_clause}"
        
        result = self.execute_query(sql, where_params)
        return result[0]['count'] if result else 0
    
    def get_page(self, table_name: str, page: int, page_size: int, 
                 where_clause: str = None, where_params: tuple = None, 
                 order_by: str = None) -> Dict[str, Any]:
        """分页查询"""
        # 计算总记录数
        total = self.count(table_name, where_clause, where_params)
        
        # 构建查询SQL
        sql = f"SELECT * FROM {table_name}"
        if where_clause:
            sql += f" WHERE {where_clause}"
        if order_by:
            sql += f" ORDER BY {order_by}"
        
        # 添加分页
        offset = (page - 1) * page_size
        sql += f" LIMIT {page_size} OFFSET {offset}"
        
        data = self.execute_query(sql, where_params)
        
        return {
            'data': data,
            'total': total,
            'page': page,
            'page_size': page_size,
            'total_pages': (total + page_size - 1) // page_size
        }
