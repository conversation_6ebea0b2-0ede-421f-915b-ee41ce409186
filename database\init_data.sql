-- JX进销存管理信息系统初始化数据
USE jx_inventory_system;

-- ========== 码表数据初始化 ==========
-- 商品种类码表
DELETE FROM jx_c_sort;
INSERT INTO jx_c_sort (sort_code, sort_name) VALUES ('01', '食品类');
INSERT INTO jx_c_sort (sort_code, sort_name) VALUES ('02', '服装类');
INSERT INTO jx_c_sort (sort_code, sort_name) VALUES ('03', '电器类');
INSERT INTO jx_c_sort (sort_code, sort_name) VALUES ('04', '日用品');

-- 商品产地码表
DELETE FROM jx_c_aero;
INSERT INTO jx_c_aero (aero_code, aero_name) VALUES ('01', '中国北京');
INSERT INTO jx_c_aero (aero_code, aero_name) VALUES ('02', '中国上海');
INSERT INTO jx_c_aero (aero_code, aero_name) VALUES ('03', '中国广州');
INSERT INTO jx_c_aero (aero_code, aero_name) VALUES ('04', '中国深圳');
INSERT INTO jx_c_aero (aero_code, aero_name) VALUES ('05', '中国杭州');
INSERT INTO jx_c_aero (aero_code, aero_name) VALUES ('06', '中国南京');
INSERT INTO jx_c_aero (aero_code, aero_name) VALUES ('07', '美国加州');

-- 商品计量单位码表
DELETE FROM jx_c_unit;
INSERT INTO jx_c_unit (unit_code, unit_name) VALUES ('01', '个');
INSERT INTO jx_c_unit (unit_code, unit_name) VALUES ('02', '件');
INSERT INTO jx_c_unit (unit_code, unit_name) VALUES ('03', '台');
INSERT INTO jx_c_unit (unit_code, unit_name) VALUES ('04', '套');

-- ========== 基本信息表数据初始化 ==========
-- 部门信息表
DELETE FROM jx_d_dept;
INSERT INTO jx_d_dept (dept_code, dept_name) VALUES ('01', '销售部');
INSERT INTO jx_d_dept (dept_code, dept_name) VALUES ('02', '采购部');
INSERT INTO jx_d_dept (dept_code, dept_name) VALUES ('03', '财务部');
INSERT INTO jx_d_dept (dept_code, dept_name) VALUES ('04', '仓储部');

-- 操作员信息表
DELETE FROM jx_d_operator;
INSERT INTO jx_d_operator (oper_code, oper_name, password, dept_code, power) VALUES ('admin', '系统管理员', '123456', '03', '全部权限');
INSERT INTO jx_d_operator (oper_code, oper_name, password, dept_code, power) VALUES ('sales01', '销售员1', '123456', '01', '销售权限');
INSERT INTO jx_d_operator (oper_code, oper_name, password, dept_code, power) VALUES ('purchase01', '采购员1', '123456', '02', '采购权限');
INSERT INTO jx_d_operator (oper_code, oper_name, password, dept_code, power) VALUES ('finance01', '财务员1', '123456', '03', '财务权限');

-- 往来单位信息表
DELETE FROM jx_d_supp;
INSERT INTO jx_d_supp (supp_code, supp_name, zip, addr, tel, fax, email, web, account, bank) 
VALUES ('S001', '北京供应商A', '100000', '北京市朝阳区', '010-********', '010-********', '<EMAIL>', 'www.supplier.com', '********90', '中国银行北京分行');
INSERT INTO jx_d_supp (supp_code, supp_name, zip, addr, tel, fax, email, web, account, bank) 
VALUES ('S002', '上海供应商B', '200000', '上海市浦东新区', '021-********', '021-********', '<EMAIL>', 'www.supplier2.com', '**********', '工商银行上海分行');
INSERT INTO jx_d_supp (supp_code, supp_name, zip, addr, tel, fax, email, web, account, bank) 
VALUES ('C001', '广州客户A', '510000', '广州市天河区', '020-********', '020-********', '<EMAIL>', 'www.customer.com', '**********', '建设银行广州分行');
INSERT INTO jx_d_supp (supp_code, supp_name, zip, addr, tel, fax, email, web, account, bank) 
VALUES ('C002', '深圳客户B', '518000', '深圳市南山区', '0755-********', '0755-********', '<EMAIL>', 'www.customer2.com', '**********', '招商银行深圳分行');

-- 商品信息表
DELETE FROM jx_d_goods;
INSERT INTO jx_d_goods (goods_code, goods_name, sort_code, model, unit_code, price_retail, price_plan, aero_code, supp_code, note) 
VALUES ('G001', '苹果iPhone 15', '03', '128GB', '03', 6999.00, 6500.00, '07', 'S001', '最新款智能手机');
INSERT INTO jx_d_goods (goods_code, goods_name, sort_code, model, unit_code, price_retail, price_plan, aero_code, supp_code, note) 
VALUES ('G002', '联想ThinkPad X1', '03', 'i7/16G/512G', '03', 12999.00, 12000.00, '01', 'S001', '商务笔记本电脑');
INSERT INTO jx_d_goods (goods_code, goods_name, sort_code, model, unit_code, price_retail, price_plan, aero_code, supp_code, note) 
VALUES ('G003', '耐克运动鞋', '02', 'Air Max 270', '01', 899.00, 800.00, '02', 'S002', '时尚运动鞋');
INSERT INTO jx_d_goods (goods_code, goods_name, sort_code, model, unit_code, price_retail, price_plan, aero_code, supp_code, note) 
VALUES ('G004', '五常大米', '01', '5kg装', '01', 89.00, 80.00, '03', 'S002', '优质东北大米');

-- ========== 会计系统码表数据初始化 ==========
-- 科目类别码表
DELETE FROM zw_c_kmlb;
INSERT INTO zw_c_kmlb (kmlb_code, kmlb_name) VALUES ('01', '资产类');
INSERT INTO zw_c_kmlb (kmlb_code, kmlb_name) VALUES ('02', '负债类');
INSERT INTO zw_c_kmlb (kmlb_code, kmlb_name) VALUES ('03', '所有者权益');
INSERT INTO zw_c_kmlb (kmlb_code, kmlb_name) VALUES ('04', '成本类');
INSERT INTO zw_c_kmlb (kmlb_code, kmlb_name) VALUES ('05', '损益类');

-- 科目性质码表
DELETE FROM zw_c_kmxz;
INSERT INTO zw_c_kmxz (kmxz_code, kmxz_name) VALUES ('01', '借方');
INSERT INTO zw_c_kmxz (kmxz_code, kmxz_name) VALUES ('02', '贷方');

-- 币别码表
DELETE FROM zw_c_bb;
INSERT INTO zw_c_bb (bb_code, bb_name) VALUES ('01', '人民币');
INSERT INTO zw_c_bb (bb_code, bb_name) VALUES ('02', '美元');
INSERT INTO zw_c_bb (bb_code, bb_name) VALUES ('03', '欧元');

-- 行业码表
DELETE FROM zw_c_hy;
INSERT INTO zw_c_hy (hy_code, hy_name) VALUES ('01', '商业');
INSERT INTO zw_c_hy (hy_code, hy_name) VALUES ('02', '工业');
INSERT INTO zw_c_hy (hy_code, hy_name) VALUES ('03', '服务业');

-- 财务指标码表
DELETE FROM zw_c_cwzbmb;
INSERT INTO zw_c_cwzbmb (cwzb_code, cwzb_name, cwzb_dy, cwzb_jsgs) 
VALUES ('ROE', '净资产收益率', '反映企业盈利能力的重要指标', '净利润/平均净资产*100%');
INSERT INTO zw_c_cwzbmb (cwzb_code, cwzb_name, cwzb_dy, cwzb_jsgs) 
VALUES ('ROA', '总资产收益率', '反映企业资产利用效率', '净利润/平均总资产*100%');
INSERT INTO zw_c_cwzbmb (cwzb_code, cwzb_name, cwzb_dy, cwzb_jsgs) 
VALUES ('CURRENT', '流动比率', '反映企业短期偿债能力', '流动资产/流动负债');
INSERT INTO zw_c_cwzbmb (cwzb_code, cwzb_name, cwzb_dy, cwzb_jsgs) 
VALUES ('QUICK', '速动比率', '反映企业快速偿债能力', '(流动资产-存货)/流动负债');

-- 会计科目编码表
DELETE FROM zw_d_kjkmbmb;
INSERT INTO zw_d_kjkmbmb (km_code, km_name, kmlb_code, kmxz_code, yefx) VALUES ('1001', '库存现金', '01', '01', '01');
INSERT INTO zw_d_kjkmbmb (km_code, km_name, kmlb_code, kmxz_code, yefx) VALUES ('1002', '银行存款', '01', '01', '01');
INSERT INTO zw_d_kjkmbmb (km_code, km_name, kmlb_code, kmxz_code, yefx) VALUES ('1121', '应收账款', '01', '01', '01');
INSERT INTO zw_d_kjkmbmb (km_code, km_name, kmlb_code, kmxz_code, yefx) VALUES ('1401', '材料采购', '01', '01', '01');
INSERT INTO zw_d_kjkmbmb (km_code, km_name, kmlb_code, kmxz_code, yefx) VALUES ('1402', '在途物资', '01', '01', '01');
INSERT INTO zw_d_kjkmbmb (km_code, km_name, kmlb_code, kmxz_code, yefx) VALUES ('1403', '原材料', '01', '01', '01');
INSERT INTO zw_d_kjkmbmb (km_code, km_name, kmlb_code, kmxz_code, yefx) VALUES ('1405', '库存商品', '01', '01', '01');
INSERT INTO zw_d_kjkmbmb (km_code, km_name, kmlb_code, kmxz_code, yefx) VALUES ('2202', '应付账款', '02', '02', '02');
INSERT INTO zw_d_kjkmbmb (km_code, km_name, kmlb_code, kmxz_code, yefx) VALUES ('4001', '生产成本', '04', '01', '01');
INSERT INTO zw_d_kjkmbmb (km_code, km_name, kmlb_code, kmxz_code, yefx) VALUES ('6001', '主营业务收入', '05', '02', '02');
INSERT INTO zw_d_kjkmbmb (km_code, km_name, kmlb_code, kmxz_code, yefx) VALUES ('6401', '主营业务成本', '05', '01', '01');

-- 操作员表
DELETE FROM zw_d_czy;
INSERT INTO zw_d_czy (czy_code, czy_name, mm) VALUES ('admin', '系统管理员', '123456');
INSERT INTO zw_d_czy (czy_code, czy_name, mm) VALUES ('finance01', '财务员1', '123456');

-- 账套信息表
DELETE FROM zw_d_ztxxb;
INSERT INTO zw_d_ztxxb (zth, ztmc, hy_code, bb_code, qjs, qsrq, jsrq, qyrq, nkjqj, zt) 
VALUES ('01', 'JX进销存系统账套', '01', '01', 1, '2024-01-01', '2024-12-31', '2024-01-01', '2024', '正常');
