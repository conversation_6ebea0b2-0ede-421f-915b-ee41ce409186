# -*- coding: utf-8 -*-
"""
业务单据管理窗口
"""

from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QFormLayout,
                            QLineEdit, QPushButton, QLabel, QMessageBox,
                            QTableWidgetItem, QComboBox, QTextEdit, QDateEdit,
                            QSpinBox, QDoubleSpinBox, QTableWidget, QHeaderView,
                            QAbstractItemView, QSplitter, QGroupBox, QGridLayout)
from PyQt5.QtCore import Qt, QDate, pyqtSignal
from PyQt5.QtGui import QDoubleValidator, QIntValidator
from models import PurchaseDAO, SalesDAO, OperatorDAO, SuppDAO, GoodsDAO
from .base_table_widget import BaseTableWidget
from utils import logger
from datetime import datetime, date


class PurchaseDialog(QDialog):
    """采购单编辑对话框"""
    
    def __init__(self, parent=None, title="编辑采购单", data=None):
        super().__init__(parent)
        self.data = data
        self.purchase_dao = PurchaseDAO()
        self.oper_dao = OperatorDAO()
        self.supp_dao = SuppDAO()
        self.goods_dao = GoodsDAO()
        self.items_data = []
        
        self.init_ui(title)
        self.load_combo_data()
        if data:
            self.load_data()
        else:
            # 新建时设置默认值
            self.date_edit.setDate(QDate.currentDate())
    
    def init_ui(self, title):
        """初始化UI"""
        self.setWindowTitle(title)
        self.setModal(True)
        self.resize(1000, 700)
        
        layout = QVBoxLayout()
        
        # 创建分割器
        splitter = QSplitter(Qt.Vertical)
        
        # 主表信息组
        main_group = QGroupBox("采购单信息")
        main_layout = QGridLayout()
        
        # 第一行
        main_layout.addWidget(QLabel("单据日期:"), 0, 0)
        self.date_edit = QDateEdit()
        self.date_edit.setCalendarPopup(True)
        main_layout.addWidget(self.date_edit, 0, 1)
        
        main_layout.addWidget(QLabel("操作员:"), 0, 2)
        self.oper_combo = QComboBox()
        main_layout.addWidget(self.oper_combo, 0, 3)
        
        # 第二行
        main_layout.addWidget(QLabel("供应商:"), 1, 0)
        self.supp_combo = QComboBox()
        main_layout.addWidget(self.supp_combo, 1, 1)
        
        main_layout.addWidget(QLabel("总金额:"), 1, 2)
        self.mone_edit = QLineEdit()
        self.mone_edit.setValidator(QDoubleValidator(0.00, 999999.99, 2))
        self.mone_edit.setReadOnly(True)
        main_layout.addWidget(self.mone_edit, 1, 3)
        
        # 第三行
        main_layout.addWidget(QLabel("税额:"), 2, 0)
        self.tax_edit = QLineEdit()
        self.tax_edit.setValidator(QDoubleValidator(0.00, 999999.99, 2))
        main_layout.addWidget(self.tax_edit, 2, 1)
        
        # 第四行
        main_layout.addWidget(QLabel("备注:"), 3, 0)
        self.note_edit = QTextEdit()
        self.note_edit.setMaximumHeight(60)
        main_layout.addWidget(self.note_edit, 3, 1, 1, 3)
        
        main_group.setLayout(main_layout)
        splitter.addWidget(main_group)
        
        # 明细表组
        detail_group = QGroupBox("采购明细")
        detail_layout = QVBoxLayout()
        
        # 明细操作按钮
        detail_btn_layout = QHBoxLayout()
        self.add_item_btn = QPushButton("添加明细")
        self.add_item_btn.clicked.connect(self.add_item)
        detail_btn_layout.addWidget(self.add_item_btn)
        
        self.edit_item_btn = QPushButton("编辑明细")
        self.edit_item_btn.clicked.connect(self.edit_item)
        detail_btn_layout.addWidget(self.edit_item_btn)
        
        self.delete_item_btn = QPushButton("删除明细")
        self.delete_item_btn.clicked.connect(self.delete_item)
        detail_btn_layout.addWidget(self.delete_item_btn)
        
        detail_btn_layout.addStretch()
        detail_layout.addLayout(detail_btn_layout)
        
        # 明细表格
        self.items_table = QTableWidget()
        self.items_table.setColumnCount(7)
        self.items_table.setHorizontalHeaderLabels([
            "商品代码", "商品名称", "数量", "单价", "金额", "税额", "备注"
        ])
        self.items_table.horizontalHeader().setStretchLastSection(True)
        self.items_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.items_table.setAlternatingRowColors(True)
        detail_layout.addWidget(self.items_table)
        
        detail_group.setLayout(detail_layout)
        splitter.addWidget(detail_group)
        
        # 设置分割器比例
        splitter.setSizes([200, 400])
        layout.addWidget(splitter)
        
        # 按钮布局
        button_layout = QHBoxLayout()
        
        self.ok_btn = QPushButton("确定")
        self.ok_btn.clicked.connect(self.accept)
        button_layout.addWidget(self.ok_btn)
        
        self.cancel_btn = QPushButton("取消")
        self.cancel_btn.clicked.connect(self.reject)
        button_layout.addWidget(self.cancel_btn)
        
        layout.addLayout(button_layout)
        self.setLayout(layout)
    
    def load_combo_data(self):
        """加载下拉框数据"""
        try:
            # 加载操作员
            operators = self.oper_dao.get_all_operators()
            self.oper_combo.clear()
            self.oper_combo.addItem("", "")
            for oper in operators:
                self.oper_combo.addItem(oper['oper_name'], oper['oper_code'])
            
            # 加载供应商
            supps = self.supp_dao.get_all_supps()
            self.supp_combo.clear()
            self.supp_combo.addItem("", "")
            for supp in supps:
                self.supp_combo.addItem(supp['supp_name'], supp['supp_code'])
                
        except Exception as e:
            logger.error(f"加载下拉框数据失败: {e}")
            QMessageBox.warning(self, "警告", f"加载数据失败: {e}")
    
    def load_data(self):
        """加载数据"""
        if not self.data:
            return
        
        try:
            # 加载主表数据
            if self.data.get('sheetdate'):
                self.date_edit.setDate(QDate.fromString(str(self.data['sheetdate']), "yyyy-MM-dd"))
            
            # 设置下拉框选中项
            oper_code = self.data.get('oper_code', '')
            for i in range(self.oper_combo.count()):
                if self.oper_combo.itemData(i) == oper_code:
                    self.oper_combo.setCurrentIndex(i)
                    break
            
            supp_code = self.data.get('supp_code', '')
            for i in range(self.supp_combo.count()):
                if self.supp_combo.itemData(i) == supp_code:
                    self.supp_combo.setCurrentIndex(i)
                    break
            
            self.mone_edit.setText(str(self.data.get('mone', 0)))
            self.tax_edit.setText(str(self.data.get('tax', 0)))
            self.note_edit.setPlainText(self.data.get('note', ''))
            
            # 加载明细数据
            if 'sheetid' in self.data:
                items = self.purchase_dao.get_purchase_items(self.data['sheetid'])
                self.items_data = items
                self.refresh_items_table()
                
        except Exception as e:
            logger.error(f"加载数据失败: {e}")
            QMessageBox.warning(self, "警告", f"加载数据失败: {e}")
    
    def refresh_items_table(self):
        """刷新明细表格"""
        self.items_table.setRowCount(len(self.items_data))
        
        total_mone = 0
        for i, item in enumerate(self.items_data):
            self.items_table.setItem(i, 0, QTableWidgetItem(str(item.get('goods_code', ''))))
            self.items_table.setItem(i, 1, QTableWidgetItem(str(item.get('goods_name', ''))))
            self.items_table.setItem(i, 2, QTableWidgetItem(str(item.get('amount', 0))))
            self.items_table.setItem(i, 3, QTableWidgetItem(str(item.get('price', 0))))
            
            mone = item.get('mone', 0)
            self.items_table.setItem(i, 4, QTableWidgetItem(str(mone)))
            self.items_table.setItem(i, 5, QTableWidgetItem(str(item.get('tax', 0))))
            self.items_table.setItem(i, 6, QTableWidgetItem(str(item.get('note', ''))))
            
            total_mone += float(mone) if mone else 0
        
        # 更新总金额
        self.mone_edit.setText(str(total_mone))
    
    def add_item(self):
        """添加明细"""
        dialog = PurchaseItemDialog(self, "添加采购明细")
        if dialog.exec_() == QDialog.Accepted:
            item_data = dialog.get_data()
            self.items_data.append(item_data)
            self.refresh_items_table()
    
    def edit_item(self):
        """编辑明细"""
        current_row = self.items_table.currentRow()
        if current_row < 0:
            QMessageBox.information(self, "提示", "请选择要编辑的明细行")
            return
        
        item_data = self.items_data[current_row]
        dialog = PurchaseItemDialog(self, "编辑采购明细", item_data)
        if dialog.exec_() == QDialog.Accepted:
            self.items_data[current_row] = dialog.get_data()
            self.refresh_items_table()
    
    def delete_item(self):
        """删除明细"""
        current_row = self.items_table.currentRow()
        if current_row < 0:
            QMessageBox.information(self, "提示", "请选择要删除的明细行")
            return
        
        reply = QMessageBox.question(self, "确认", "确定要删除选中的明细行吗？",
                                   QMessageBox.Yes | QMessageBox.No)
        if reply == QMessageBox.Yes:
            del self.items_data[current_row]
            self.refresh_items_table()
    
    def get_data(self):
        """获取表单数据"""
        main_data = {
            'sheetdate': self.date_edit.date().toPyDate(),
            'oper_code': self.oper_combo.currentData(),
            'supp_code': self.supp_combo.currentData(),
            'mone': float(self.mone_edit.text()) if self.mone_edit.text() else 0,
            'tax': float(self.tax_edit.text()) if self.tax_edit.text() else 0,
            'note': self.note_edit.toPlainText()
        }
        
        return main_data, self.items_data
    
    def validate_data(self):
        """验证数据"""
        if not self.oper_combo.currentData():
            QMessageBox.warning(self, "警告", "请选择操作员")
            return False
        
        if not self.supp_combo.currentData():
            QMessageBox.warning(self, "警告", "请选择供应商")
            return False
        
        if not self.items_data:
            QMessageBox.warning(self, "警告", "请添加采购明细")
            return False
        
        return True
    
    def accept(self):
        """确认按钮点击"""
        if self.validate_data():
            super().accept()


class PurchaseItemDialog(QDialog):
    """采购明细编辑对话框"""

    def __init__(self, parent=None, title="编辑采购明细", data=None):
        super().__init__(parent)
        self.data = data
        self.goods_dao = GoodsDAO()
        self.init_ui(title)
        self.load_goods()
        if data:
            self.load_data()

    def init_ui(self, title):
        """初始化UI"""
        self.setWindowTitle(title)
        self.setModal(True)
        self.setFixedSize(400, 300)

        layout = QVBoxLayout()

        # 表单布局
        form_layout = QFormLayout()

        self.goods_combo = QComboBox()
        self.goods_combo.currentTextChanged.connect(self.on_goods_changed)
        form_layout.addRow("商品:", self.goods_combo)

        self.amount_spin = QSpinBox()
        self.amount_spin.setRange(1, 999999)
        self.amount_spin.valueChanged.connect(self.calculate_mone)
        form_layout.addRow("数量:", self.amount_spin)

        self.price_spin = QDoubleSpinBox()
        self.price_spin.setRange(0.01, 999999.99)
        self.price_spin.setDecimals(2)
        self.price_spin.valueChanged.connect(self.calculate_mone)
        form_layout.addRow("单价:", self.price_spin)

        self.mone_edit = QLineEdit()
        self.mone_edit.setReadOnly(True)
        form_layout.addRow("金额:", self.mone_edit)

        self.tax_spin = QDoubleSpinBox()
        self.tax_spin.setRange(0.00, 999999.99)
        self.tax_spin.setDecimals(2)
        form_layout.addRow("税额:", self.tax_spin)

        self.note_edit = QTextEdit()
        self.note_edit.setMaximumHeight(60)
        form_layout.addRow("备注:", self.note_edit)

        layout.addLayout(form_layout)

        # 按钮布局
        button_layout = QHBoxLayout()

        self.ok_btn = QPushButton("确定")
        self.ok_btn.clicked.connect(self.accept)
        button_layout.addWidget(self.ok_btn)

        self.cancel_btn = QPushButton("取消")
        self.cancel_btn.clicked.connect(self.reject)
        button_layout.addWidget(self.cancel_btn)

        layout.addLayout(button_layout)
        self.setLayout(layout)

    def load_goods(self):
        """加载商品数据"""
        try:
            goods_list = self.goods_dao.get_all_goods()
            self.goods_combo.clear()
            self.goods_combo.addItem("", "")
            for goods in goods_list:
                display_text = f"{goods['goods_code']} - {goods['goods_name']}"
                self.goods_combo.addItem(display_text, goods['goods_code'])
        except Exception as e:
            logger.error(f"加载商品数据失败: {e}")

    def on_goods_changed(self):
        """商品选择改变"""
        goods_code = self.goods_combo.currentData()
        if goods_code:
            try:
                goods = self.goods_dao.get_goods_by_code(goods_code)
                if goods and goods.get('price_plan'):
                    self.price_spin.setValue(float(goods['price_plan']))
            except Exception as e:
                logger.error(f"获取商品价格失败: {e}")

    def calculate_mone(self):
        """计算金额"""
        amount = self.amount_spin.value()
        price = self.price_spin.value()
        mone = amount * price
        self.mone_edit.setText(f"{mone:.2f}")

    def load_data(self):
        """加载数据"""
        if not self.data:
            return

        # 设置商品
        goods_code = self.data.get('goods_code', '')
        for i in range(self.goods_combo.count()):
            if self.goods_combo.itemData(i) == goods_code:
                self.goods_combo.setCurrentIndex(i)
                break

        self.amount_spin.setValue(int(self.data.get('amount', 1)))
        self.price_spin.setValue(float(self.data.get('price', 0)))
        self.tax_spin.setValue(float(self.data.get('tax', 0)))
        self.note_edit.setPlainText(self.data.get('note', ''))

        self.calculate_mone()

    def get_data(self):
        """获取数据"""
        return {
            'goods_code': self.goods_combo.currentData(),
            'amount': self.amount_spin.value(),
            'price': self.price_spin.value(),
            'mone': float(self.mone_edit.text()) if self.mone_edit.text() else 0,
            'tax': self.tax_spin.value(),
            'note': self.note_edit.toPlainText()
        }

    def validate_data(self):
        """验证数据"""
        if not self.goods_combo.currentData():
            QMessageBox.warning(self, "警告", "请选择商品")
            return False

        if self.amount_spin.value() <= 0:
            QMessageBox.warning(self, "警告", "数量必须大于0")
            return False

        if self.price_spin.value() <= 0:
            QMessageBox.warning(self, "警告", "单价必须大于0")
            return False

        return True

    def accept(self):
        """确认按钮点击"""
        if self.validate_data():
            super().accept()


class PurchaseManagementWindow(BaseTableWidget):
    """采购单管理窗口"""

    def __init__(self):
        super().__init__("采购单管理")
        self.purchase_dao = PurchaseDAO()
        self.setup_table()
        self.load_data()

    def setup_table(self):
        """设置表格"""
        # 设置表格列
        self.table.setColumnCount(9)
        self.table.setHorizontalHeaderLabels([
            "单据号", "单据日期", "操作员", "供应商", "金额", "税额",
            "确认状态", "确认人", "确认日期"
        ])

        # 设置列宽
        header = self.table.horizontalHeader()
        header.resizeSection(0, 120)  # 单据号
        header.resizeSection(1, 100)  # 单据日期
        header.resizeSection(2, 80)   # 操作员
        header.resizeSection(3, 120)  # 供应商
        header.resizeSection(4, 80)   # 金额
        header.resizeSection(5, 80)   # 税额
        header.resizeSection(6, 80)   # 确认状态
        header.resizeSection(7, 80)   # 确认人
        header.resizeSection(8, 100)  # 确认日期

        # 添加确认按钮
        self.confirm_btn = QPushButton("确认单据")
        self.confirm_btn.clicked.connect(self.confirm_purchase)
        self.button_layout.insertWidget(3, self.confirm_btn)

    def load_data(self):
        """加载数据"""
        try:
            search_text = self.search_edit.text().strip()
            data, total = self.purchase_dao.get_all_purchases(
                1, 50, search_text  # 简化分页，暂时使用固定值
            )

            self.table.setRowCount(len(data))
            for i, row in enumerate(data):
                self.table.setItem(i, 0, QTableWidgetItem(str(row.get('sheetid', ''))))
                self.table.setItem(i, 1, QTableWidgetItem(str(row.get('sheetdate', ''))))
                self.table.setItem(i, 2, QTableWidgetItem(str(row.get('oper_name', ''))))
                self.table.setItem(i, 3, QTableWidgetItem(str(row.get('supp_name', ''))))
                self.table.setItem(i, 4, QTableWidgetItem(str(row.get('mone', 0))))
                self.table.setItem(i, 5, QTableWidgetItem(str(row.get('tax', 0))))

                # 确认状态
                flag_qr = row.get('flag_qr', '0')
                status_text = "已确认" if flag_qr == '1' else "未确认"
                self.table.setItem(i, 6, QTableWidgetItem(status_text))

                self.table.setItem(i, 7, QTableWidgetItem(str(row.get('oper_qr_name', ''))))
                self.table.setItem(i, 8, QTableWidgetItem(str(row.get('date_qr', ''))))

        except Exception as e:
            logger.error(f"加载采购单数据失败: {e}")
            QMessageBox.warning(self, "警告", f"加载数据失败: {e}")

    def search_data(self):
        """搜索数据"""
        self.load_data()

    def add_item(self):
        """添加项目"""
        dialog = PurchaseDialog(self, "新增采购单")
        if dialog.exec_() == QDialog.Accepted:
            try:
                main_data, items_data = dialog.get_data()
                if self.purchase_dao.add_purchase(main_data, items_data):
                    QMessageBox.information(self, "成功", "添加采购单成功")
                    self.load_data()
                else:
                    QMessageBox.warning(self, "失败", "添加采购单失败")
            except Exception as e:
                logger.error(f"添加采购单失败: {e}")
                QMessageBox.warning(self, "错误", f"添加失败: {e}")

    def edit_item(self):
        """编辑项目"""
        current_row = self.table.currentRow()
        if current_row < 0:
            QMessageBox.information(self, "提示", "请选择要编辑的记录")
            return

        sheet_id = self.table.item(current_row, 0).text()

        try:
            # 获取完整数据
            purchase_data = self.purchase_dao.get_purchase_by_id(sheet_id)
            if not purchase_data:
                QMessageBox.warning(self, "错误", "获取采购单数据失败")
                return

            dialog = PurchaseDialog(self, "编辑采购单", purchase_data)
            if dialog.exec_() == QDialog.Accepted:
                main_data, items_data = dialog.get_data()
                if self.purchase_dao.update_purchase(sheet_id, main_data, items_data):
                    QMessageBox.information(self, "成功", "更新采购单成功")
                    self.load_data()
                else:
                    QMessageBox.warning(self, "失败", "更新采购单失败")

        except Exception as e:
            logger.error(f"编辑采购单失败: {e}")
            QMessageBox.warning(self, "错误", f"编辑失败: {e}")

    def delete_item(self):
        """删除项目"""
        current_row = self.table.currentRow()
        if current_row < 0:
            QMessageBox.information(self, "提示", "请选择要删除的记录")
            return

        sheet_id = self.table.item(current_row, 0).text()

        reply = QMessageBox.question(self, "确认删除",
                                   f"确定要删除采购单 {sheet_id} 吗？",
                                   QMessageBox.Yes | QMessageBox.No)

        if reply == QMessageBox.Yes:
            try:
                if self.purchase_dao.delete_purchase(sheet_id):
                    QMessageBox.information(self, "成功", "删除采购单成功")
                    self.load_data()
                else:
                    QMessageBox.warning(self, "失败", "删除采购单失败")
            except Exception as e:
                logger.error(f"删除采购单失败: {e}")
                QMessageBox.warning(self, "错误", f"删除失败: {e}")

    def confirm_purchase(self):
        """确认采购单"""
        current_row = self.table.currentRow()
        if current_row < 0:
            QMessageBox.information(self, "提示", "请选择要确认的采购单")
            return

        sheet_id = self.table.item(current_row, 0).text()
        status = self.table.item(current_row, 6).text()

        if status == "已确认":
            QMessageBox.information(self, "提示", "该采购单已经确认")
            return

        reply = QMessageBox.question(self, "确认",
                                   f"确定要确认采购单 {sheet_id} 吗？确认后不能修改。",
                                   QMessageBox.Yes | QMessageBox.No)

        if reply == QMessageBox.Yes:
            try:
                # 这里应该获取当前登录用户，暂时使用默认值
                oper_code = "admin"  # 实际应用中应该从登录信息获取

                if self.purchase_dao.confirm_purchase(sheet_id, oper_code):
                    QMessageBox.information(self, "成功", "确认采购单成功")
                    self.load_data()
                else:
                    QMessageBox.warning(self, "失败", "确认采购单失败")
            except Exception as e:
                logger.error(f"确认采购单失败: {e}")
                QMessageBox.warning(self, "错误", f"确认失败: {e}")


class SalesDialog(QDialog):
    """销售单编辑对话框"""

    def __init__(self, parent=None, title="编辑销售单", data=None):
        super().__init__(parent)
        self.data = data
        self.sales_dao = SalesDAO()
        self.oper_dao = OperatorDAO()
        self.goods_dao = GoodsDAO()
        self.items_data = []

        self.init_ui(title)
        self.load_combo_data()
        if data:
            self.load_data()
        else:
            # 新建时设置默认值
            self.date_edit.setDate(QDate.currentDate())

    def init_ui(self, title):
        """初始化UI"""
        self.setWindowTitle(title)
        self.setModal(True)
        self.resize(1000, 700)

        layout = QVBoxLayout()

        # 创建分割器
        splitter = QSplitter(Qt.Vertical)

        # 主表信息组
        main_group = QGroupBox("销售单信息")
        main_layout = QGridLayout()

        # 第一行
        main_layout.addWidget(QLabel("单据日期:"), 0, 0)
        self.date_edit = QDateEdit()
        self.date_edit.setCalendarPopup(True)
        main_layout.addWidget(self.date_edit, 0, 1)

        main_layout.addWidget(QLabel("操作员:"), 0, 2)
        self.oper_combo = QComboBox()
        main_layout.addWidget(self.oper_combo, 0, 3)

        # 第二行
        main_layout.addWidget(QLabel("总金额:"), 1, 0)
        self.mone_edit = QLineEdit()
        self.mone_edit.setValidator(QDoubleValidator(0.00, 999999.99, 2))
        self.mone_edit.setReadOnly(True)
        main_layout.addWidget(self.mone_edit, 1, 1)

        main_layout.addWidget(QLabel("税额:"), 1, 2)
        self.tax_edit = QLineEdit()
        self.tax_edit.setValidator(QDoubleValidator(0.00, 999999.99, 2))
        main_layout.addWidget(self.tax_edit, 1, 3)

        # 第三行
        main_layout.addWidget(QLabel("备注:"), 2, 0)
        self.note_edit = QTextEdit()
        self.note_edit.setMaximumHeight(60)
        main_layout.addWidget(self.note_edit, 2, 1, 1, 3)

        main_group.setLayout(main_layout)
        splitter.addWidget(main_group)

        # 明细表组
        detail_group = QGroupBox("销售明细")
        detail_layout = QVBoxLayout()

        # 明细操作按钮
        detail_btn_layout = QHBoxLayout()
        self.add_item_btn = QPushButton("添加明细")
        self.add_item_btn.clicked.connect(self.add_item)
        detail_btn_layout.addWidget(self.add_item_btn)

        self.edit_item_btn = QPushButton("编辑明细")
        self.edit_item_btn.clicked.connect(self.edit_item)
        detail_btn_layout.addWidget(self.edit_item_btn)

        self.delete_item_btn = QPushButton("删除明细")
        self.delete_item_btn.clicked.connect(self.delete_item)
        detail_btn_layout.addWidget(self.delete_item_btn)

        detail_btn_layout.addStretch()
        detail_layout.addLayout(detail_btn_layout)

        # 明细表格
        self.items_table = QTableWidget()
        self.items_table.setColumnCount(8)
        self.items_table.setHorizontalHeaderLabels([
            "商品代码", "商品名称", "数量", "单价", "折扣", "金额", "税额", "备注"
        ])
        self.items_table.horizontalHeader().setStretchLastSection(True)
        self.items_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.items_table.setAlternatingRowColors(True)
        detail_layout.addWidget(self.items_table)

        detail_group.setLayout(detail_layout)
        splitter.addWidget(detail_group)

        # 设置分割器比例
        splitter.setSizes([200, 400])
        layout.addWidget(splitter)

        # 按钮布局
        button_layout = QHBoxLayout()

        self.ok_btn = QPushButton("确定")
        self.ok_btn.clicked.connect(self.accept)
        button_layout.addWidget(self.ok_btn)

        self.cancel_btn = QPushButton("取消")
        self.cancel_btn.clicked.connect(self.reject)
        button_layout.addWidget(self.cancel_btn)

        layout.addLayout(button_layout)
        self.setLayout(layout)

    def load_combo_data(self):
        """加载下拉框数据"""
        try:
            # 加载操作员
            operators = self.oper_dao.get_all_operators()
            self.oper_combo.clear()
            self.oper_combo.addItem("", "")
            for oper in operators:
                self.oper_combo.addItem(oper['oper_name'], oper['oper_code'])

        except Exception as e:
            logger.error(f"加载下拉框数据失败: {e}")
            QMessageBox.warning(self, "警告", f"加载数据失败: {e}")

    def load_data(self):
        """加载数据"""
        if not self.data:
            return

        try:
            # 加载主表数据
            if self.data.get('sheetdate'):
                self.date_edit.setDate(QDate.fromString(str(self.data['sheetdate']), "yyyy-MM-dd"))

            # 设置下拉框选中项
            oper_code = self.data.get('oper_code', '')
            for i in range(self.oper_combo.count()):
                if self.oper_combo.itemData(i) == oper_code:
                    self.oper_combo.setCurrentIndex(i)
                    break

            self.mone_edit.setText(str(self.data.get('mone', 0)))
            self.tax_edit.setText(str(self.data.get('tax', 0)))
            self.note_edit.setPlainText(self.data.get('note', ''))

            # 加载明细数据
            if 'sheetid' in self.data:
                items = self.sales_dao.get_sales_items(self.data['sheetid'])
                self.items_data = items
                self.refresh_items_table()

        except Exception as e:
            logger.error(f"加载数据失败: {e}")
            QMessageBox.warning(self, "警告", f"加载数据失败: {e}")

    def refresh_items_table(self):
        """刷新明细表格"""
        self.items_table.setRowCount(len(self.items_data))

        total_mone = 0
        for i, item in enumerate(self.items_data):
            self.items_table.setItem(i, 0, QTableWidgetItem(str(item.get('goods_code', ''))))
            self.items_table.setItem(i, 1, QTableWidgetItem(str(item.get('goods_name', ''))))
            self.items_table.setItem(i, 2, QTableWidgetItem(str(item.get('amount', 0))))
            self.items_table.setItem(i, 3, QTableWidgetItem(str(item.get('price', 0))))
            self.items_table.setItem(i, 4, QTableWidgetItem(str(item.get('discount', 1))))

            mone = item.get('mone', 0)
            self.items_table.setItem(i, 5, QTableWidgetItem(str(mone)))
            self.items_table.setItem(i, 6, QTableWidgetItem(str(item.get('tax', 0))))
            self.items_table.setItem(i, 7, QTableWidgetItem(str(item.get('note', ''))))

            total_mone += float(mone) if mone else 0

        # 更新总金额
        self.mone_edit.setText(str(total_mone))

    def add_item(self):
        """添加明细"""
        dialog = SalesItemDialog(self, "添加销售明细")
        if dialog.exec_() == QDialog.Accepted:
            item_data = dialog.get_data()
            self.items_data.append(item_data)
            self.refresh_items_table()

    def edit_item(self):
        """编辑明细"""
        current_row = self.items_table.currentRow()
        if current_row < 0:
            QMessageBox.information(self, "提示", "请选择要编辑的明细行")
            return

        item_data = self.items_data[current_row]
        dialog = SalesItemDialog(self, "编辑销售明细", item_data)
        if dialog.exec_() == QDialog.Accepted:
            self.items_data[current_row] = dialog.get_data()
            self.refresh_items_table()

    def delete_item(self):
        """删除明细"""
        current_row = self.items_table.currentRow()
        if current_row < 0:
            QMessageBox.information(self, "提示", "请选择要删除的明细行")
            return

        reply = QMessageBox.question(self, "确认", "确定要删除选中的明细行吗？",
                                   QMessageBox.Yes | QMessageBox.No)
        if reply == QMessageBox.Yes:
            del self.items_data[current_row]
            self.refresh_items_table()

    def get_data(self):
        """获取表单数据"""
        main_data = {
            'sheetdate': self.date_edit.date().toPyDate(),
            'oper_code': self.oper_combo.currentData(),
            'mone': float(self.mone_edit.text()) if self.mone_edit.text() else 0,
            'tax': float(self.tax_edit.text()) if self.tax_edit.text() else 0,
            'note': self.note_edit.toPlainText()
        }

        return main_data, self.items_data

    def validate_data(self):
        """验证数据"""
        if not self.oper_combo.currentData():
            QMessageBox.warning(self, "警告", "请选择操作员")
            return False

        if not self.items_data:
            QMessageBox.warning(self, "警告", "请添加销售明细")
            return False

        return True

    def accept(self):
        """确认按钮点击"""
        if self.validate_data():
            super().accept()


class SalesItemDialog(QDialog):
    """销售明细编辑对话框"""

    def __init__(self, parent=None, title="编辑销售明细", data=None):
        super().__init__(parent)
        self.data = data
        self.goods_dao = GoodsDAO()
        self.init_ui(title)
        self.load_goods()
        if data:
            self.load_data()

    def init_ui(self, title):
        """初始化UI"""
        self.setWindowTitle(title)
        self.setModal(True)
        self.setFixedSize(400, 350)

        layout = QVBoxLayout()

        # 表单布局
        form_layout = QFormLayout()

        self.goods_combo = QComboBox()
        self.goods_combo.currentTextChanged.connect(self.on_goods_changed)
        form_layout.addRow("商品:", self.goods_combo)

        self.amount_spin = QSpinBox()
        self.amount_spin.setRange(1, 999999)
        self.amount_spin.valueChanged.connect(self.calculate_mone)
        form_layout.addRow("数量:", self.amount_spin)

        self.price_spin = QDoubleSpinBox()
        self.price_spin.setRange(0.01, 999999.99)
        self.price_spin.setDecimals(2)
        self.price_spin.valueChanged.connect(self.calculate_mone)
        form_layout.addRow("单价:", self.price_spin)

        self.discount_spin = QDoubleSpinBox()
        self.discount_spin.setRange(0.001, 1.000)
        self.discount_spin.setDecimals(3)
        self.discount_spin.setValue(1.000)
        self.discount_spin.valueChanged.connect(self.calculate_mone)
        form_layout.addRow("折扣:", self.discount_spin)

        self.mone_edit = QLineEdit()
        self.mone_edit.setReadOnly(True)
        form_layout.addRow("金额:", self.mone_edit)

        self.tax_spin = QDoubleSpinBox()
        self.tax_spin.setRange(0.00, 999999.99)
        self.tax_spin.setDecimals(2)
        form_layout.addRow("税额:", self.tax_spin)

        self.note_edit = QTextEdit()
        self.note_edit.setMaximumHeight(60)
        form_layout.addRow("备注:", self.note_edit)

        layout.addLayout(form_layout)

        # 按钮布局
        button_layout = QHBoxLayout()

        self.ok_btn = QPushButton("确定")
        self.ok_btn.clicked.connect(self.accept)
        button_layout.addWidget(self.ok_btn)

        self.cancel_btn = QPushButton("取消")
        self.cancel_btn.clicked.connect(self.reject)
        button_layout.addWidget(self.cancel_btn)

        layout.addLayout(button_layout)
        self.setLayout(layout)

    def load_goods(self):
        """加载商品数据"""
        try:
            goods_list = self.goods_dao.get_all_goods()
            self.goods_combo.clear()
            self.goods_combo.addItem("", "")
            for goods in goods_list:
                display_text = f"{goods['goods_code']} - {goods['goods_name']}"
                self.goods_combo.addItem(display_text, goods['goods_code'])
        except Exception as e:
            logger.error(f"加载商品数据失败: {e}")

    def on_goods_changed(self):
        """商品选择改变"""
        goods_code = self.goods_combo.currentData()
        if goods_code:
            try:
                goods = self.goods_dao.get_goods_by_code(goods_code)
                if goods and goods.get('price_retail'):
                    self.price_spin.setValue(float(goods['price_retail']))
            except Exception as e:
                logger.error(f"获取商品价格失败: {e}")

    def calculate_mone(self):
        """计算金额"""
        amount = self.amount_spin.value()
        price = self.price_spin.value()
        discount = self.discount_spin.value()
        mone = amount * price * discount
        self.mone_edit.setText(f"{mone:.2f}")

    def load_data(self):
        """加载数据"""
        if not self.data:
            return

        # 设置商品
        goods_code = self.data.get('goods_code', '')
        for i in range(self.goods_combo.count()):
            if self.goods_combo.itemData(i) == goods_code:
                self.goods_combo.setCurrentIndex(i)
                break

        self.amount_spin.setValue(int(self.data.get('amount', 1)))
        self.price_spin.setValue(float(self.data.get('price', 0)))
        self.discount_spin.setValue(float(self.data.get('discount', 1)))
        self.tax_spin.setValue(float(self.data.get('tax', 0)))
        self.note_edit.setPlainText(self.data.get('note', ''))

        self.calculate_mone()

    def get_data(self):
        """获取数据"""
        return {
            'goods_code': self.goods_combo.currentData(),
            'amount': self.amount_spin.value(),
            'price': self.price_spin.value(),
            'discount': self.discount_spin.value(),
            'mone': float(self.mone_edit.text()) if self.mone_edit.text() else 0,
            'tax': self.tax_spin.value(),
            'note': self.note_edit.toPlainText()
        }

    def validate_data(self):
        """验证数据"""
        if not self.goods_combo.currentData():
            QMessageBox.warning(self, "警告", "请选择商品")
            return False

        if self.amount_spin.value() <= 0:
            QMessageBox.warning(self, "警告", "数量必须大于0")
            return False

        if self.price_spin.value() <= 0:
            QMessageBox.warning(self, "警告", "单价必须大于0")
            return False

        return True

    def accept(self):
        """确认按钮点击"""
        if self.validate_data():
            super().accept()


class SalesManagementWindow(BaseTableWidget):
    """销售单管理窗口"""

    def __init__(self):
        super().__init__("销售单管理")
        self.sales_dao = SalesDAO()
        self.setup_table()
        self.load_data()

    def setup_table(self):
        """设置表格"""
        # 设置表格列
        self.table.setColumnCount(8)
        self.table.setHorizontalHeaderLabels([
            "单据号", "单据日期", "操作员", "金额", "税额",
            "确认状态", "确认人", "确认日期"
        ])

        # 设置列宽
        header = self.table.horizontalHeader()
        header.resizeSection(0, 120)  # 单据号
        header.resizeSection(1, 100)  # 单据日期
        header.resizeSection(2, 80)   # 操作员
        header.resizeSection(3, 80)   # 金额
        header.resizeSection(4, 80)   # 税额
        header.resizeSection(5, 80)   # 确认状态
        header.resizeSection(6, 80)   # 确认人
        header.resizeSection(7, 100)  # 确认日期

        # 添加确认按钮
        self.confirm_btn = QPushButton("确认单据")
        self.confirm_btn.clicked.connect(self.confirm_sales)
        self.button_layout.insertWidget(4, self.confirm_btn)

    def load_data(self):
        """加载数据"""
        try:
            search_text = self.search_edit.text().strip()
            data, total = self.sales_dao.get_all_sales(
                1, 50, search_text  # 简化分页，暂时使用固定值
            )

            self.table.setRowCount(len(data))
            for i, row in enumerate(data):
                self.table.setItem(i, 0, QTableWidgetItem(str(row.get('sheetid', ''))))
                self.table.setItem(i, 1, QTableWidgetItem(str(row.get('sheetdate', ''))))
                self.table.setItem(i, 2, QTableWidgetItem(str(row.get('oper_name', ''))))
                self.table.setItem(i, 3, QTableWidgetItem(str(row.get('mone', 0))))
                self.table.setItem(i, 4, QTableWidgetItem(str(row.get('tax', 0))))

                # 确认状态
                flag_qr = row.get('flag_qr', '0')
                status_text = "已确认" if flag_qr == '1' else "未确认"
                self.table.setItem(i, 5, QTableWidgetItem(status_text))

                self.table.setItem(i, 6, QTableWidgetItem(str(row.get('oper_qr_name', ''))))
                self.table.setItem(i, 7, QTableWidgetItem(str(row.get('date_qr', ''))))

        except Exception as e:
            logger.error(f"加载销售单数据失败: {e}")
            QMessageBox.warning(self, "警告", f"加载数据失败: {e}")

    def search_data(self):
        """搜索数据"""
        self.load_data()

    def add_item(self):
        """添加项目"""
        dialog = SalesDialog(self, "新增销售单")
        if dialog.exec_() == QDialog.Accepted:
            try:
                main_data, items_data = dialog.get_data()
                if self.sales_dao.add_sales(main_data, items_data):
                    QMessageBox.information(self, "成功", "添加销售单成功")
                    self.load_data()
                else:
                    QMessageBox.warning(self, "失败", "添加销售单失败")
            except Exception as e:
                logger.error(f"添加销售单失败: {e}")
                QMessageBox.warning(self, "错误", f"添加失败: {e}")

    def edit_item(self):
        """编辑项目"""
        current_row = self.table.currentRow()
        if current_row < 0:
            QMessageBox.information(self, "提示", "请选择要编辑的记录")
            return

        sheet_id = self.table.item(current_row, 0).text()

        try:
            # 获取完整数据
            sales_data = self.sales_dao.get_sales_by_id(sheet_id)
            if not sales_data:
                QMessageBox.warning(self, "错误", "获取销售单数据失败")
                return

            dialog = SalesDialog(self, "编辑销售单", sales_data)
            if dialog.exec_() == QDialog.Accepted:
                main_data, items_data = dialog.get_data()
                if self.sales_dao.update_sales(sheet_id, main_data, items_data):
                    QMessageBox.information(self, "成功", "更新销售单成功")
                    self.load_data()
                else:
                    QMessageBox.warning(self, "失败", "更新销售单失败")

        except Exception as e:
            logger.error(f"编辑销售单失败: {e}")
            QMessageBox.warning(self, "错误", f"编辑失败: {e}")

    def delete_item(self):
        """删除项目"""
        current_row = self.table.currentRow()
        if current_row < 0:
            QMessageBox.information(self, "提示", "请选择要删除的记录")
            return

        sheet_id = self.table.item(current_row, 0).text()

        reply = QMessageBox.question(self, "确认删除",
                                   f"确定要删除销售单 {sheet_id} 吗？",
                                   QMessageBox.Yes | QMessageBox.No)

        if reply == QMessageBox.Yes:
            try:
                if self.sales_dao.delete_sales(sheet_id):
                    QMessageBox.information(self, "成功", "删除销售单成功")
                    self.load_data()
                else:
                    QMessageBox.warning(self, "失败", "删除销售单失败")
            except Exception as e:
                logger.error(f"删除销售单失败: {e}")
                QMessageBox.warning(self, "错误", f"删除失败: {e}")

    def confirm_sales(self):
        """确认销售单"""
        current_row = self.table.currentRow()
        if current_row < 0:
            QMessageBox.information(self, "提示", "请选择要确认的销售单")
            return

        sheet_id = self.table.item(current_row, 0).text()
        status = self.table.item(current_row, 5).text()

        if status == "已确认":
            QMessageBox.information(self, "提示", "该销售单已经确认")
            return

        reply = QMessageBox.question(self, "确认",
                                   f"确定要确认销售单 {sheet_id} 吗？确认后不能修改。",
                                   QMessageBox.Yes | QMessageBox.No)

        if reply == QMessageBox.Yes:
            try:
                # 这里应该获取当前登录用户，暂时使用默认值
                oper_code = "admin"  # 实际应用中应该从登录信息获取

                if self.sales_dao.confirm_sales(sheet_id, oper_code):
                    QMessageBox.information(self, "成功", "确认销售单成功")
                    self.load_data()
                else:
                    QMessageBox.warning(self, "失败", "确认销售单失败")
            except Exception as e:
                logger.error(f"确认销售单失败: {e}")
                QMessageBox.warning(self, "错误", f"确认失败: {e}")
