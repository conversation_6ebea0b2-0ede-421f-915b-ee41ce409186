# -*- coding: utf-8 -*-
"""
码表数据访问对象
"""

from typing import List, Dict, Any, Optional
from .base_dao import BaseDAO

class CodeTableDAO(BaseDAO):
    """码表DAO类"""
    
    def __init__(self):
        super().__init__()

class SortDAO(CodeTableDAO):
    """商品种类码表DAO"""
    
    def __init__(self):
        super().__init__()
        self.table_name = 'jx_c_sort'
    
    def get_all_sorts(self) -> List[Dict[str, Any]]:
        """获取所有商品种类"""
        return self.get_all(self.table_name, 'sort_code')
    
    def get_sort_by_code(self, sort_code: str) -> Optional[Dict[str, Any]]:
        """根据种类代码获取种类信息"""
        return self.get_by_id(self.table_name, 'sort_code', sort_code)
    
    def add_sort(self, sort_code: str, sort_name: str) -> bool:
        """添加商品种类"""
        data = {
            'sort_code': sort_code,
            'sort_name': sort_name
        }
        return self.insert(self.table_name, data)
    
    def update_sort(self, sort_code: str, sort_name: str) -> bool:
        """更新商品种类"""
        data = {'sort_name': sort_name}
        return self.update(self.table_name, data, 'sort_code = %s', (sort_code,))
    
    def delete_sort(self, sort_code: str) -> bool:
        """删除商品种类"""
        return self.delete(self.table_name, 'sort_code = %s', (sort_code,))
    
    def sort_exists(self, sort_code: str) -> bool:
        """检查种类代码是否存在"""
        return self.exists(self.table_name, 'sort_code = %s', (sort_code,))

class UnitDAO(CodeTableDAO):
    """商品计量单位码表DAO"""
    
    def __init__(self):
        super().__init__()
        self.table_name = 'jx_c_unit'
    
    def get_all_units(self) -> List[Dict[str, Any]]:
        """获取所有计量单位"""
        return self.get_all(self.table_name, 'unit_code')
    
    def get_unit_by_code(self, unit_code: str) -> Optional[Dict[str, Any]]:
        """根据单位代码获取单位信息"""
        return self.get_by_id(self.table_name, 'unit_code', unit_code)
    
    def add_unit(self, unit_code: str, unit_name: str) -> bool:
        """添加计量单位"""
        data = {
            'unit_code': unit_code,
            'unit_name': unit_name
        }
        return self.insert(self.table_name, data)
    
    def update_unit(self, unit_code: str, unit_name: str) -> bool:
        """更新计量单位"""
        data = {'unit_name': unit_name}
        return self.update(self.table_name, data, 'unit_code = %s', (unit_code,))
    
    def delete_unit(self, unit_code: str) -> bool:
        """删除计量单位"""
        return self.delete(self.table_name, 'unit_code = %s', (unit_code,))
    
    def unit_exists(self, unit_code: str) -> bool:
        """检查单位代码是否存在"""
        return self.exists(self.table_name, 'unit_code = %s', (unit_code,))

class AeroDAO(CodeTableDAO):
    """商品产地码表DAO"""
    
    def __init__(self):
        super().__init__()
        self.table_name = 'jx_c_aero'
    
    def get_all_aeros(self) -> List[Dict[str, Any]]:
        """获取所有产地"""
        return self.get_all(self.table_name, 'aero_code')
    
    def get_aero_by_code(self, aero_code: str) -> Optional[Dict[str, Any]]:
        """根据产地代码获取产地信息"""
        return self.get_by_id(self.table_name, 'aero_code', aero_code)
    
    def add_aero(self, aero_code: str, aero_name: str) -> bool:
        """添加产地"""
        data = {
            'aero_code': aero_code,
            'aero_name': aero_name
        }
        return self.insert(self.table_name, data)
    
    def update_aero(self, aero_code: str, aero_name: str) -> bool:
        """更新产地"""
        data = {'aero_name': aero_name}
        return self.update(self.table_name, data, 'aero_code = %s', (aero_code,))
    
    def delete_aero(self, aero_code: str) -> bool:
        """删除产地"""
        return self.delete(self.table_name, 'aero_code = %s', (aero_code,))
    
    def aero_exists(self, aero_code: str) -> bool:
        """检查产地代码是否存在"""
        return self.exists(self.table_name, 'aero_code = %s', (aero_code,))

class DeptDAO(CodeTableDAO):
    """部门信息表DAO"""
    
    def __init__(self):
        super().__init__()
        self.table_name = 'jx_d_dept'
    
    def get_all_depts(self) -> List[Dict[str, Any]]:
        """获取所有部门"""
        return self.get_all(self.table_name, 'dept_code')
    
    def get_dept_by_code(self, dept_code: str) -> Optional[Dict[str, Any]]:
        """根据部门代码获取部门信息"""
        return self.get_by_id(self.table_name, 'dept_code', dept_code)
    
    def add_dept(self, dept_code: str, dept_name: str) -> bool:
        """添加部门"""
        data = {
            'dept_code': dept_code,
            'dept_name': dept_name
        }
        return self.insert(self.table_name, data)
    
    def update_dept(self, dept_code: str, dept_name: str) -> bool:
        """更新部门"""
        data = {'dept_name': dept_name}
        return self.update(self.table_name, data, 'dept_code = %s', (dept_code,))
    
    def delete_dept(self, dept_code: str) -> bool:
        """删除部门"""
        return self.delete(self.table_name, 'dept_code = %s', (dept_code,))
    
    def dept_exists(self, dept_code: str) -> bool:
        """检查部门代码是否存在"""
        return self.exists(self.table_name, 'dept_code = %s', (dept_code,))
