# JX进销存管理信息系统 - 功能演示指南

## 系统启动

### 1. 环境准备
确保已安装以下软件：
- Python 3.7+
- MySQL 8.0+
- PyQt5

### 2. 启动系统
```bash
# 进入项目目录
cd c:\Users\<USER>\Desktop\毕设

# 启动系统
python main.py
```

### 3. 系统登录
- **用户名**: admin
- **密码**: 123456
- 点击"登录"按钮进入系统

## 功能演示

### 一、码表管理功能

#### 1. 商品种类管理
- **路径**: 基础数据 → 码表管理 → 商品种类
- **功能**: 
  - 查看所有商品种类
  - 添加新的商品种类
  - 修改现有商品种类
  - 删除商品种类
  - 搜索商品种类

**演示步骤**:
1. 点击菜单"基础数据" → "码表管理" → "商品种类"
2. 查看现有的4条商品种类记录
3. 点击"添加"按钮，输入新的种类代码和名称
4. 选择一条记录，点击"修改"按钮进行编辑
5. 选择一条记录，点击"删除"按钮删除记录
6. 在搜索框中输入关键字进行搜索

#### 2. 计量单位管理
- **路径**: 基础数据 → 码表管理 → 计量单位
- **功能**: 与商品种类管理相同
- **数据**: 包含"个"、"件"、"台"、"套"等4个单位

#### 3. 商品产地管理
- **路径**: 基础数据 → 码表管理 → 商品产地
- **功能**: 与商品种类管理相同
- **数据**: 包含北京、上海、广州等7个产地

#### 4. 部门信息管理
- **路径**: 基础数据 → 码表管理 → 部门信息
- **功能**: 与商品种类管理相同
- **数据**: 包含销售部、采购部、财务部、仓储部等4个部门

### 二、基本信息管理功能

#### 1. 操作员信息管理
- **路径**: 基础数据 → 基本信息 → 操作员信息
- **功能**:
  - 查看所有操作员信息（包含部门关联）
  - 添加新操作员（支持部门选择）
  - 修改操作员信息
  - 删除操作员
  - 搜索操作员

**演示步骤**:
1. 点击菜单"基础数据" → "基本信息" → "操作员信息"
2. 查看现有的4个操作员记录
3. 点击"添加"按钮，填写操作员信息：
   - 操作员代码：test01
   - 操作员姓名：测试用户
   - 密码：123456
   - 所属部门：从下拉框选择
   - 权限：测试权限
4. 双击记录或点击"修改"按钮编辑信息
5. 测试搜索功能

#### 2. 往来单位信息管理
- **路径**: 基础数据 → 基本信息 → 往来单位
- **功能**:
  - 查看所有往来单位信息
  - 添加新的往来单位（完整的联系信息）
  - 修改往来单位信息
  - 删除往来单位
  - 搜索往来单位

**演示步骤**:
1. 点击菜单"基础数据" → "基本信息" → "往来单位"
2. 查看现有的4个往来单位记录
3. 点击"添加"按钮，填写详细信息：
   - 单位代码：C003
   - 单位名称：测试客户
   - 邮编：100000
   - 地址：北京市朝阳区
   - 电话：010-12345678
   - 传真：010-12345679
   - 邮箱：<EMAIL>
   - 网址：www.test.com
   - 银行账号：1234567890
   - 开户银行：中国银行
4. 测试修改和删除功能

#### 3. 商品信息管理
- **路径**: 基础数据 → 基本信息 → 商品信息
- **功能**:
  - 查看所有商品信息（包含关联的种类、单位、产地、供应商）
  - 添加新商品（支持下拉框选择关联数据）
  - 修改商品信息
  - 删除商品
  - 搜索商品

**演示步骤**:
1. 点击菜单"基础数据" → "基本信息" → "商品信息"
2. 查看现有的3个商品记录
3. 点击"添加"按钮，填写商品信息：
   - 商品代码：G005
   - 商品名称：测试商品
   - 商品种类：从下拉框选择
   - 规格型号：测试规格
   - 计量单位：从下拉框选择
   - 零售价：99.00
   - 计划价：90.00
   - 产地：从下拉框选择
   - 供应商：从下拉框选择
   - 备注：这是一个测试商品
4. 测试修改和删除功能

### 三、系统特色功能

#### 1. 数据关联显示
- 商品信息显示时会自动关联显示种类名称、单位名称、产地名称、供应商名称
- 操作员信息显示时会自动关联显示部门名称
- 所有关联数据都是实时从数据库获取的最新信息

#### 2. 数据验证
- 代码唯一性验证：添加时检查代码是否已存在
- 必填字段验证：确保关键信息不为空
- 数据格式验证：如价格必须为数字格式

#### 3. 用户友好界面
- 现代化的界面设计
- 支持搜索过滤
- 双击编辑功能
- 确认删除对话框
- 操作结果提示

#### 4. 错误处理
- 完善的异常处理机制
- 详细的错误日志记录
- 用户友好的错误提示

## 测试验证

### 自动化测试
运行测试脚本验证系统功能：
```bash
python test_system.py
```

测试内容包括：
- 数据库连接测试
- 码表功能测试
- 基本信息功能测试
- 增删改查操作测试
- 登录功能测试

### 手动测试建议
1. **数据完整性测试**: 添加、修改、删除数据后检查数据库中的实际数据
2. **关联数据测试**: 测试删除被其他表引用的数据时的处理
3. **并发测试**: 多个用户同时操作相同数据
4. **边界值测试**: 输入超长字符串、特殊字符等

## 系统日志

系统运行日志保存在 `logs/` 目录下，文件名格式为 `jx_system_YYYYMMDD.log`。

日志内容包括：
- 用户登录记录
- 数据库操作记录
- 错误信息记录
- 系统运行状态

## 数据库状态

当前数据库包含：
- **码表数据**: 商品种类4条、计量单位4条、产地7条、部门4条
- **基本信息**: 操作员4个、往来单位4个、商品3个
- **系统用户**: admin/123456（系统管理员）

## 下一步开发

基本信息管理功能已完成，接下来可以开发：
1. 业务单据管理（采购单、销售单等）
2. 库存管理和计算
3. 财务管理功能
4. 报表和分析功能

## 技术支持

如遇到问题，请检查：
1. MySQL服务是否正常运行
2. 数据库连接配置是否正确
3. Python依赖包是否完整安装
4. 查看日志文件获取详细错误信息
