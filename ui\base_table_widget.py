# -*- coding: utf-8 -*-
"""
基础表格管理窗口
"""

from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QPushButton,
                            QTableWidget, QTableWidgetItem, QHeaderView,
                            QMessageBox, QLineEdit, QLabel, QSpacerItem,
                            QSizePolicy, QAbstractItemView)
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont
from utils import logger

class BaseTableWidget(QWidget):
    """基础表格管理窗口类"""
    
    def __init__(self, title="数据管理"):
        super().__init__()
        self.title = title
        self.dao = None
        self.current_row = -1
        self.init_ui()
        # setup_table()和load_data()将在子类中调用
        
    def init_ui(self):
        """初始化UI"""
        layout = QVBoxLayout()
        layout.setSpacing(10)
        layout.setContentsMargins(10, 10, 10, 10)
        
        # 标题
        title_label = QLabel(self.title)
        title_font = QFont()
        title_font.setPointSize(14)
        title_font.setBold(True)
        title_label.setFont(title_font)
        title_label.setStyleSheet("color: #2c3e50; margin-bottom: 10px;")
        layout.addWidget(title_label)
        
        # 搜索栏
        search_layout = QHBoxLayout()
        search_layout.addWidget(QLabel("搜索:"))
        
        self.search_edit = QLineEdit()
        self.search_edit.setPlaceholderText("输入关键字搜索...")
        self.search_edit.textChanged.connect(self.search_data)
        search_layout.addWidget(self.search_edit)
        
        # 添加弹性空间
        spacer = QSpacerItem(40, 20, QSizePolicy.Expanding, QSizePolicy.Minimum)
        search_layout.addItem(spacer)
        
        layout.addLayout(search_layout)
        
        # 表格
        self.table = QTableWidget()
        self.table.setAlternatingRowColors(True)
        self.table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.table.setSelectionMode(QAbstractItemView.SingleSelection)
        self.table.horizontalHeader().setStretchLastSection(True)
        self.table.verticalHeader().setVisible(False)
        self.table.itemSelectionChanged.connect(self.on_selection_changed)
        self.table.itemDoubleClicked.connect(self.edit_item)
        
        # 设置表格样式
        self.table.setStyleSheet("""
            QTableWidget {
                gridline-color: #d0d0d0;
                background-color: white;
                alternate-background-color: #f8f9fa;
                selection-background-color: #3498db;
                selection-color: white;
            }
            QTableWidget::item {
                padding: 8px;
                border: none;
            }
            QHeaderView::section {
                background-color: #ecf0f1;
                padding: 8px;
                border: 1px solid #bdc3c7;
                font-weight: bold;
                color: #2c3e50;
            }
        """)
        
        layout.addWidget(self.table)
        
        # 按钮栏
        self.button_layout = QHBoxLayout()
        
        self.add_btn = QPushButton("添加")
        self.add_btn.clicked.connect(self.add_item)
        self.button_layout.addWidget(self.add_btn)

        self.edit_btn = QPushButton("修改")
        self.edit_btn.clicked.connect(self.edit_item)
        self.edit_btn.setEnabled(False)
        self.button_layout.addWidget(self.edit_btn)

        self.delete_btn = QPushButton("删除")
        self.delete_btn.clicked.connect(self.delete_item)
        self.delete_btn.setEnabled(False)
        self.button_layout.addWidget(self.delete_btn)

        self.refresh_btn = QPushButton("刷新")
        self.refresh_btn.clicked.connect(self.load_data)
        self.button_layout.addWidget(self.refresh_btn)

        # 添加弹性空间
        spacer = QSpacerItem(40, 20, QSizePolicy.Expanding, QSizePolicy.Minimum)
        self.button_layout.addItem(spacer)

        layout.addLayout(self.button_layout)
        
        # 设置按钮样式
        button_style = """
            QPushButton {
                padding: 8px 16px;
                border: none;
                border-radius: 4px;
                font-size: 12px;
                font-weight: bold;
                color: white;
                min-width: 60px;
            }
            QPushButton:hover {
                opacity: 0.8;
            }
            QPushButton:disabled {
                background-color: #bdc3c7;
                color: #7f8c8d;
            }
        """
        
        self.add_btn.setStyleSheet(button_style + "QPushButton { background-color: #27ae60; }")
        self.edit_btn.setStyleSheet(button_style + "QPushButton { background-color: #f39c12; }")
        self.delete_btn.setStyleSheet(button_style + "QPushButton { background-color: #e74c3c; }")
        self.refresh_btn.setStyleSheet(button_style + "QPushButton { background-color: #3498db; }")
        
        self.setLayout(layout)
        
    def setup_table(self):
        """设置表格（子类重写）"""
        pass
        
    def load_data(self):
        """加载数据（子类重写）"""
        pass
        
    def search_data(self):
        """搜索数据（子类重写）"""
        pass
        
    def add_item(self):
        """添加项目（子类重写）"""
        pass
        
    def edit_item(self):
        """编辑项目（子类重写）"""
        pass
        
    def delete_item(self):
        """删除项目（子类重写）"""
        pass
        
    def on_selection_changed(self):
        """选择改变事件"""
        selected_items = self.table.selectedItems()
        has_selection = len(selected_items) > 0
        
        self.edit_btn.setEnabled(has_selection)
        self.delete_btn.setEnabled(has_selection)
        
        if has_selection:
            self.current_row = self.table.currentRow()
        else:
            self.current_row = -1
            
    def get_selected_data(self):
        """获取选中行的数据（子类重写）"""
        return None
        
    def show_message(self, title, message, msg_type="info"):
        """显示消息"""
        if msg_type == "info":
            QMessageBox.information(self, title, message)
        elif msg_type == "warning":
            QMessageBox.warning(self, title, message)
        elif msg_type == "error":
            QMessageBox.critical(self, title, message)
            
    def confirm_delete(self, item_name):
        """确认删除"""
        reply = QMessageBox.question(self, '确认删除', 
                                   f'确定要删除 "{item_name}" 吗？\n此操作不可撤销！',
                                   QMessageBox.Yes | QMessageBox.No,
                                   QMessageBox.No)
        return reply == QMessageBox.Yes
