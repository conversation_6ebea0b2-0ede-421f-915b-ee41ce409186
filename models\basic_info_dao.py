# -*- coding: utf-8 -*-
"""
基本信息表数据访问对象
"""

from typing import List, Dict, Any, Optional
from .base_dao import BaseDAO

class OperatorDAO(BaseDAO):
    """操作员信息表DAO"""
    
    def __init__(self):
        super().__init__()
        self.table_name = 'jx_d_operator'
    
    def get_all_operators(self) -> List[Dict[str, Any]]:
        """获取所有操作员"""
        sql = """
        SELECT o.*, d.dept_name 
        FROM jx_d_operator o 
        LEFT JOIN jx_d_dept d ON o.dept_code = d.dept_code 
        ORDER BY o.oper_code
        """
        return self.execute_query(sql)
    
    def get_operator_by_code(self, oper_code: str) -> Optional[Dict[str, Any]]:
        """根据操作员代码获取操作员信息"""
        sql = """
        SELECT o.*, d.dept_name 
        FROM jx_d_operator o 
        LEFT JOIN jx_d_dept d ON o.dept_code = d.dept_code 
        WHERE o.oper_code = %s
        """
        result = self.execute_query(sql, (oper_code,))
        return result[0] if result else None
    
    def add_operator(self, oper_code: str, oper_name: str, password: str, 
                    dept_code: str, power: str) -> bool:
        """添加操作员"""
        data = {
            'oper_code': oper_code,
            'oper_name': oper_name,
            'password': password,
            'dept_code': dept_code,
            'power': power
        }
        return self.insert(self.table_name, data)
    
    def update_operator(self, oper_code: str, oper_name: str, password: str, 
                       dept_code: str, power: str) -> bool:
        """更新操作员"""
        data = {
            'oper_name': oper_name,
            'password': password,
            'dept_code': dept_code,
            'power': power
        }
        return self.update(self.table_name, data, 'oper_code = %s', (oper_code,))
    
    def delete_operator(self, oper_code: str) -> bool:
        """删除操作员"""
        return self.delete(self.table_name, 'oper_code = %s', (oper_code,))
    
    def operator_exists(self, oper_code: str) -> bool:
        """检查操作员代码是否存在"""
        return self.exists(self.table_name, 'oper_code = %s', (oper_code,))
    
    def validate_login(self, oper_code: str, password: str) -> Optional[Dict[str, Any]]:
        """验证登录"""
        sql = """
        SELECT o.*, d.dept_name 
        FROM jx_d_operator o 
        LEFT JOIN jx_d_dept d ON o.dept_code = d.dept_code 
        WHERE o.oper_code = %s AND o.password = %s
        """
        result = self.execute_query(sql, (oper_code, password))
        return result[0] if result else None

class SuppDAO(BaseDAO):
    """往来单位信息表DAO"""
    
    def __init__(self):
        super().__init__()
        self.table_name = 'jx_d_supp'
    
    def get_all_supps(self) -> List[Dict[str, Any]]:
        """获取所有往来单位"""
        return self.get_all(self.table_name, 'supp_code')
    
    def get_supp_by_code(self, supp_code: str) -> Optional[Dict[str, Any]]:
        """根据单位代码获取单位信息"""
        return self.get_by_id(self.table_name, 'supp_code', supp_code)
    
    def add_supp(self, supp_code: str, supp_name: str, zip_code: str = None,
                addr: str = None, tel: str = None, fax: str = None,
                email: str = None, web: str = None, account: str = None,
                bank: str = None) -> bool:
        """添加往来单位"""
        data = {
            'supp_code': supp_code,
            'supp_name': supp_name,
            'zip': zip_code,
            'addr': addr,
            'tel': tel,
            'fax': fax,
            'email': email,
            'web': web,
            'account': account,
            'bank': bank
        }
        return self.insert(self.table_name, data)
    
    def update_supp(self, supp_code: str, supp_name: str, zip_code: str = None,
                   addr: str = None, tel: str = None, fax: str = None,
                   email: str = None, web: str = None, account: str = None,
                   bank: str = None) -> bool:
        """更新往来单位"""
        data = {
            'supp_name': supp_name,
            'zip': zip_code,
            'addr': addr,
            'tel': tel,
            'fax': fax,
            'email': email,
            'web': web,
            'account': account,
            'bank': bank
        }
        return self.update(self.table_name, data, 'supp_code = %s', (supp_code,))
    
    def delete_supp(self, supp_code: str) -> bool:
        """删除往来单位"""
        return self.delete(self.table_name, 'supp_code = %s', (supp_code,))
    
    def supp_exists(self, supp_code: str) -> bool:
        """检查单位代码是否存在"""
        return self.exists(self.table_name, 'supp_code = %s', (supp_code,))
    
    def search_supps(self, keyword: str) -> List[Dict[str, Any]]:
        """搜索往来单位"""
        sql = """
        SELECT * FROM jx_d_supp 
        WHERE supp_code LIKE %s OR supp_name LIKE %s 
        ORDER BY supp_code
        """
        keyword_pattern = f"%{keyword}%"
        return self.execute_query(sql, (keyword_pattern, keyword_pattern))

class GoodsDAO(BaseDAO):
    """商品信息表DAO"""
    
    def __init__(self):
        super().__init__()
        self.table_name = 'jx_d_goods'
    
    def get_all_goods(self) -> List[Dict[str, Any]]:
        """获取所有商品"""
        sql = """
        SELECT g.*, s.sort_name, u.unit_name, a.aero_name, sp.supp_name
        FROM jx_d_goods g
        LEFT JOIN jx_c_sort s ON g.sort_code = s.sort_code
        LEFT JOIN jx_c_unit u ON g.unit_code = u.unit_code
        LEFT JOIN jx_c_aero a ON g.aero_code = a.aero_code
        LEFT JOIN jx_d_supp sp ON g.supp_code = sp.supp_code
        ORDER BY g.goods_code
        """
        return self.execute_query(sql)
    
    def get_goods_by_code(self, goods_code: str) -> Optional[Dict[str, Any]]:
        """根据商品代码获取商品信息"""
        sql = """
        SELECT g.*, s.sort_name, u.unit_name, a.aero_name, sp.supp_name
        FROM jx_d_goods g
        LEFT JOIN jx_c_sort s ON g.sort_code = s.sort_code
        LEFT JOIN jx_c_unit u ON g.unit_code = u.unit_code
        LEFT JOIN jx_c_aero a ON g.aero_code = a.aero_code
        LEFT JOIN jx_d_supp sp ON g.supp_code = sp.supp_code
        WHERE g.goods_code = %s
        """
        result = self.execute_query(sql, (goods_code,))
        return result[0] if result else None
    
    def add_goods(self, goods_code: str, goods_name: str, sort_code: str = None,
                 model: str = None, unit_code: str = None, price_retail: float = None,
                 price_plan: float = None, aero_code: str = None, supp_code: str = None,
                 note: str = None, photofile: str = None) -> bool:
        """添加商品"""
        data = {
            'goods_code': goods_code,
            'goods_name': goods_name,
            'sort_code': sort_code,
            'model': model,
            'unit_code': unit_code,
            'price_retail': price_retail,
            'price_plan': price_plan,
            'aero_code': aero_code,
            'supp_code': supp_code,
            'note': note,
            'photofile': photofile
        }
        return self.insert(self.table_name, data)
    
    def update_goods(self, goods_code: str, goods_name: str, sort_code: str = None,
                    model: str = None, unit_code: str = None, price_retail: float = None,
                    price_plan: float = None, aero_code: str = None, supp_code: str = None,
                    note: str = None, photofile: str = None) -> bool:
        """更新商品"""
        data = {
            'goods_name': goods_name,
            'sort_code': sort_code,
            'model': model,
            'unit_code': unit_code,
            'price_retail': price_retail,
            'price_plan': price_plan,
            'aero_code': aero_code,
            'supp_code': supp_code,
            'note': note,
            'photofile': photofile
        }
        return self.update(self.table_name, data, 'goods_code = %s', (goods_code,))
    
    def delete_goods(self, goods_code: str) -> bool:
        """删除商品"""
        return self.delete(self.table_name, 'goods_code = %s', (goods_code,))
    
    def goods_exists(self, goods_code: str) -> bool:
        """检查商品代码是否存在"""
        return self.exists(self.table_name, 'goods_code = %s', (goods_code,))
    
    def search_goods(self, keyword: str) -> List[Dict[str, Any]]:
        """搜索商品"""
        sql = """
        SELECT g.*, s.sort_name, u.unit_name, a.aero_name, sp.supp_name
        FROM jx_d_goods g
        LEFT JOIN jx_c_sort s ON g.sort_code = s.sort_code
        LEFT JOIN jx_c_unit u ON g.unit_code = u.unit_code
        LEFT JOIN jx_c_aero a ON g.aero_code = a.aero_code
        LEFT JOIN jx_d_supp sp ON g.supp_code = sp.supp_code
        WHERE g.goods_code LIKE %s OR g.goods_name LIKE %s
        ORDER BY g.goods_code
        """
        keyword_pattern = f"%{keyword}%"
        return self.execute_query(sql, (keyword_pattern, keyword_pattern))
