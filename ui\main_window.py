# -*- coding: utf-8 -*-
"""
主窗口界面
"""

import sys
from PyQt5.QtWidgets import (QApplication, QMainWindow, QMenuBar, QMenu, QAction, 
                            QToolBar, QStatusBar, QMdiArea, QMdiSubWindow,
                            QVBoxLayout, QWidget, QMessageBox, QLabel)
from PyQt5.QtCore import Qt, pyqtSignal
from PyQt5.QtGui import QIcon, QFont
from utils import logger

class MainWindow(QMainWindow):
    """主窗口类"""
    
    # 定义信号
    login_required = pyqtSignal()
    
    def __init__(self):
        super().__init__()
        self.current_user = None
        self.init_ui()
        self.setup_menu()
        self.setup_toolbar()
        self.setup_statusbar()
        
    def init_ui(self):
        """初始化UI"""
        self.setWindowTitle("JX进销存管理信息系统")
        self.setGeometry(100, 100, 1200, 800)
        
        # 设置中央窗口为MDI区域
        self.mdi_area = QMdiArea()
        self.setCentralWidget(self.mdi_area)
        
        # 设置窗口样式
        self.setStyleSheet("""
            QMainWindow {
                background-color: #f0f0f0;
            }
            QMenuBar {
                background-color: #2c3e50;
                color: white;
                border: none;
                padding: 4px;
            }
            QMenuBar::item {
                background-color: transparent;
                padding: 8px 12px;
                margin: 2px;
                border-radius: 4px;
            }
            QMenuBar::item:selected {
                background-color: #34495e;
            }
            QMenu {
                background-color: white;
                border: 1px solid #bdc3c7;
                padding: 4px;
            }
            QMenu::item {
                padding: 8px 20px;
                border-radius: 4px;
            }
            QMenu::item:selected {
                background-color: #3498db;
                color: white;
            }
            QToolBar {
                background-color: #ecf0f1;
                border: none;
                spacing: 4px;
                padding: 4px;
            }
            QStatusBar {
                background-color: #34495e;
                color: white;
                border: none;
            }
        """)
        
    def setup_menu(self):
        """设置菜单栏"""
        menubar = self.menuBar()
        
        # 系统菜单
        system_menu = menubar.addMenu('系统管理')
        
        login_action = QAction('用户登录', self)
        login_action.setShortcut('Ctrl+L')
        login_action.triggered.connect(self.show_login)
        system_menu.addAction(login_action)
        
        logout_action = QAction('退出登录', self)
        logout_action.triggered.connect(self.logout)
        system_menu.addAction(logout_action)
        
        system_menu.addSeparator()
        
        exit_action = QAction('退出系统', self)
        exit_action.setShortcut('Ctrl+Q')
        exit_action.triggered.connect(self.close)
        system_menu.addAction(exit_action)
        
        # 基础数据菜单
        basic_menu = menubar.addMenu('基础数据')
        
        # 码表管理子菜单
        code_submenu = basic_menu.addMenu('码表管理')
        
        sort_action = QAction('商品种类', self)
        sort_action.triggered.connect(self.show_sort_management)
        code_submenu.addAction(sort_action)
        
        unit_action = QAction('计量单位', self)
        unit_action.triggered.connect(self.show_unit_management)
        code_submenu.addAction(unit_action)
        
        aero_action = QAction('商品产地', self)
        aero_action.triggered.connect(self.show_aero_management)
        code_submenu.addAction(aero_action)
        
        # 基本信息子菜单
        info_submenu = basic_menu.addMenu('基本信息')
        
        dept_action = QAction('部门信息', self)
        dept_action.triggered.connect(self.show_dept_management)
        info_submenu.addAction(dept_action)
        
        operator_action = QAction('操作员信息', self)
        operator_action.triggered.connect(self.show_operator_management)
        info_submenu.addAction(operator_action)
        
        supp_action = QAction('往来单位', self)
        supp_action.triggered.connect(self.show_supp_management)
        info_submenu.addAction(supp_action)
        
        goods_action = QAction('商品信息', self)
        goods_action.triggered.connect(self.show_goods_management)
        info_submenu.addAction(goods_action)
        
        # 业务处理菜单
        business_menu = menubar.addMenu('业务处理')
        
        purchase_action = QAction('采购管理', self)
        purchase_action.triggered.connect(self.show_purchase_management)
        business_menu.addAction(purchase_action)
        
        sales_action = QAction('销售管理', self)
        sales_action.triggered.connect(self.show_sales_management)
        business_menu.addAction(sales_action)
        
        inventory_action = QAction('库存管理', self)
        inventory_action.triggered.connect(self.show_inventory_management)
        business_menu.addAction(inventory_action)
        
        # 财务管理菜单
        finance_menu = menubar.addMenu('财务管理')
        
        voucher_action = QAction('凭证管理', self)
        voucher_action.triggered.connect(self.show_voucher_management)
        finance_menu.addAction(voucher_action)
        
        account_action = QAction('账表管理', self)
        account_action.triggered.connect(self.show_account_management)
        finance_menu.addAction(account_action)
        
        report_action = QAction('财务报表', self)
        report_action.triggered.connect(self.show_report_management)
        finance_menu.addAction(report_action)
        
        # 统计分析菜单
        analysis_menu = menubar.addMenu('统计分析')
        
        cost_action = QAction('成本分析', self)
        cost_action.triggered.connect(self.show_cost_analysis)
        analysis_menu.addAction(cost_action)
        
        visual_action = QAction('可视化分析', self)
        visual_action.triggered.connect(self.show_visual_analysis)
        analysis_menu.addAction(visual_action)
        
        audit_action = QAction('稽核审计', self)
        audit_action.triggered.connect(self.show_audit_analysis)
        analysis_menu.addAction(audit_action)
        
        # 帮助菜单
        help_menu = menubar.addMenu('帮助')
        
        about_action = QAction('关于系统', self)
        about_action.triggered.connect(self.show_about)
        help_menu.addAction(about_action)
        
    def setup_toolbar(self):
        """设置工具栏"""
        toolbar = QToolBar()
        self.addToolBar(toolbar)
        
        # 添加常用功能按钮
        login_action = QAction('登录', self)
        login_action.triggered.connect(self.show_login)
        toolbar.addAction(login_action)
        
        toolbar.addSeparator()
        
        goods_action = QAction('商品管理', self)
        goods_action.triggered.connect(self.show_goods_management)
        toolbar.addAction(goods_action)
        
        purchase_action = QAction('采购管理', self)
        purchase_action.triggered.connect(self.show_purchase_management)
        toolbar.addAction(purchase_action)
        
        sales_action = QAction('销售管理', self)
        sales_action.triggered.connect(self.show_sales_management)
        toolbar.addAction(sales_action)
        
        toolbar.addSeparator()
        
        report_action = QAction('财务报表', self)
        report_action.triggered.connect(self.show_report_management)
        toolbar.addAction(report_action)
        
    def setup_statusbar(self):
        """设置状态栏"""
        self.statusbar = QStatusBar()
        self.setStatusBar(self.statusbar)
        
        # 添加状态信息
        self.user_label = QLabel("未登录")
        self.statusbar.addWidget(self.user_label)
        
        self.statusbar.addPermanentWidget(QLabel("JX进销存管理信息系统 v1.0"))
        
    def set_current_user(self, user_info):
        """设置当前用户"""
        self.current_user = user_info
        if user_info:
            self.user_label.setText(f"当前用户: {user_info['oper_name']} ({user_info['dept_name']})")
        else:
            self.user_label.setText("未登录")
    
    def check_login(self):
        """检查登录状态"""
        if not self.current_user:
            QMessageBox.warning(self, "提示", "请先登录系统！")
            self.show_login()
            return False
        return True

    # 菜单事件处理方法
    def show_login(self):
        """显示登录窗口"""
        from .login_dialog import LoginDialog
        dialog = LoginDialog(self)
        if dialog.exec_() == dialog.Accepted:
            user_info = dialog.get_user_info()
            self.set_current_user(user_info)
            self.statusbar.showMessage("重新登录成功", 3000)

    def logout(self):
        """退出登录"""
        self.set_current_user(None)
        # 关闭所有子窗口
        for window in self.mdi_area.subWindowList():
            window.close()
        self.statusbar.showMessage("已退出登录", 3000)

    def show_sort_management(self):
        """显示商品种类管理"""
        if not self.check_login():
            return
        from .code_table_windows import SortManagementWindow
        self.show_sub_window(SortManagementWindow(), "商品种类管理")

    def show_unit_management(self):
        """显示计量单位管理"""
        if not self.check_login():
            return
        from .code_table_windows import UnitManagementWindow
        self.show_sub_window(UnitManagementWindow(), "计量单位管理")

    def show_aero_management(self):
        """显示产地管理"""
        if not self.check_login():
            return
        from .code_table_windows import AeroManagementWindow
        self.show_sub_window(AeroManagementWindow(), "产地管理")

    def show_dept_management(self):
        """显示部门管理"""
        if not self.check_login():
            return
        from .code_table_windows import DeptManagementWindow
        self.show_sub_window(DeptManagementWindow(), "部门管理")

    def show_operator_management(self):
        """显示操作员管理"""
        if not self.check_login():
            return
        from .basic_info_windows import OperatorManagementWindow
        self.show_sub_window(OperatorManagementWindow(), "操作员管理")

    def show_supp_management(self):
        """显示往来单位管理"""
        if not self.check_login():
            return
        from .basic_info_windows import SuppManagementWindow
        self.show_sub_window(SuppManagementWindow(), "往来单位管理")

    def show_goods_management(self):
        """显示商品管理"""
        if not self.check_login():
            return
        from .basic_info_windows import GoodsManagementWindow
        self.show_sub_window(GoodsManagementWindow(), "商品管理")

    def show_purchase_management(self):
        """显示采购管理"""
        if not self.check_login():
            return
        from .business_windows import PurchaseManagementWindow
        self.show_sub_window(PurchaseManagementWindow(), "采购管理")

    def show_sales_management(self):
        """显示销售管理"""
        if not self.check_login():
            return
        from .business_windows import SalesManagementWindow
        self.show_sub_window(SalesManagementWindow(), "销售管理")

    def show_inventory_management(self):
        """显示库存管理"""
        if not self.check_login():
            return
        QMessageBox.information(self, "提示", "库存管理功能开发中...")

    def show_voucher_management(self):
        """显示凭证管理"""
        if not self.check_login():
            return
        QMessageBox.information(self, "提示", "凭证管理功能开发中...")

    def show_account_management(self):
        """显示账表管理"""
        if not self.check_login():
            return
        QMessageBox.information(self, "提示", "账表管理功能开发中...")

    def show_report_management(self):
        """显示财务报表"""
        if not self.check_login():
            return
        QMessageBox.information(self, "提示", "财务报表功能开发中...")

    def show_cost_analysis(self):
        """显示成本分析"""
        if not self.check_login():
            return
        QMessageBox.information(self, "提示", "成本分析功能开发中...")

    def show_visual_analysis(self):
        """显示可视化分析"""
        if not self.check_login():
            return
        QMessageBox.information(self, "提示", "可视化分析功能开发中...")

    def show_audit_analysis(self):
        """显示稽核审计"""
        if not self.check_login():
            return
        QMessageBox.information(self, "提示", "稽核审计功能开发中...")

    def show_about(self):
        """显示关于信息"""
        QMessageBox.about(self, "关于系统",
                         "JX进销存管理信息系统 v1.0\n\n"
                         "基于Python + PyQt5 + MySQL开发\n"
                         "实现进销存业务管理和财务一体化处理")

    def show_sub_window(self, widget, title):
        """显示子窗口"""
        # 检查是否已经打开了相同的窗口
        for window in self.mdi_area.subWindowList():
            if window.windowTitle() == title:
                self.mdi_area.setActiveSubWindow(window)
                return

        # 创建新的子窗口
        sub_window = QMdiSubWindow()
        sub_window.setWidget(widget)
        sub_window.setWindowTitle(title)
        sub_window.setAttribute(Qt.WA_DeleteOnClose)

        self.mdi_area.addSubWindow(sub_window)
        sub_window.show()

    def closeEvent(self, event):
        """关闭事件"""
        reply = QMessageBox.question(self, '确认退出',
                                   '确定要退出系统吗？',
                                   QMessageBox.Yes | QMessageBox.No,
                                   QMessageBox.No)

        if reply == QMessageBox.Yes:
            event.accept()
        else:
            event.ignore()
