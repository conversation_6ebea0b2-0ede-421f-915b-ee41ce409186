# -*- coding: utf-8 -*-
"""
数据库配置模块
"""

import pymysql
import logging
from typing import Optional

class DatabaseConfig:
    """数据库配置类"""
    
    def __init__(self):
        self.host = 'localhost'
        self.port = 3306
        self.user = 'root'
        self.password = '200425'
        self.database = 'jx_inventory_system'
        self.charset = 'utf8mb4'
        
    def get_connection(self) -> Optional[pymysql.Connection]:
        """获取数据库连接"""
        try:
            connection = pymysql.connect(
                host=self.host,
                port=self.port,
                user=self.user,
                password=self.password,
                database=self.database,
                charset=self.charset,
                autocommit=False,
                cursorclass=pymysql.cursors.DictCursor
            )
            return connection
        except Exception as e:
            logging.error(f"数据库连接失败: {e}")
            return None
    
    def test_connection(self) -> bool:
        """测试数据库连接"""
        connection = self.get_connection()
        if connection:
            try:
                with connection.cursor() as cursor:
                    cursor.execute("SELECT 1")
                    result = cursor.fetchone()
                    return result is not None
            except Exception as e:
                logging.error(f"数据库连接测试失败: {e}")
                return False
            finally:
                connection.close()
        return False

# 全局数据库配置实例
db_config = DatabaseConfig()
