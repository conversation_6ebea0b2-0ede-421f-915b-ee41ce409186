# JX进销存管理信息系统

基于Python + PyQt5 + MySQL开发的进销存管理信息系统，实现业财融合一体化处理。

## 系统功能

### 已实现功能
1. **系统管理**
   - 用户登录/退出
   - 权限管理

2. **基础数据管理**
   - 码表管理：商品种类、计量单位、商品产地、部门信息
   - 基本信息管理：操作员信息、往来单位信息、商品信息

3. **用户界面**
   - 现代化的MDI多文档界面
   - 响应式布局设计
   - 统一的表格管理组件

### 待实现功能
1. **业务处理**
   - 采购管理（采购单主项、明细项）
   - 销售管理（销售单主项、明细项）
   - 库存管理（报损单、盘存单）
   - 收付款管理（付款单、收款单）

2. **账表管理**
   - 商品库存表
   - 流水账表
   - 应付账表、应收账表
   - 进销存数量报表

3. **成本计算**
   - 月末一次加权平均法
   - 移动加权平均法
   - 先进先出法

4. **财务管理**
   - 会计凭证管理
   - 会计账表（科目余额表、明细账表）
   - 会计报表（资产负债表、利润表、财务指标统计表、杜邦分析表）

5. **业财融合**
   - 业务单据自动生成会计凭证
   - 单据修改时凭证和报表自动更新

6. **稽核审计**
   - 账账稽核审计
   - 账证稽核
   - 账和业务单据之间的审计

7. **统计分析**
   - 可视化统计分析
   - 报表溯源功能

## 技术架构

- **前端界面**: PyQt5
- **后端语言**: Python 3.7+
- **数据库**: MySQL 8.0+
- **架构模式**: MVC分层架构

## 项目结构

```
JX进销存系统/
├── config/                 # 配置模块
│   ├── __init__.py
│   └── database.py         # 数据库配置
├── database/               # 数据库脚本
│   ├── create_tables.sql   # 建表脚本
│   ├── init_data.sql       # 初始化数据
│   └── init_database.py    # 数据库初始化工具
├── models/                 # 数据模型层
│   ├── __init__.py
│   ├── base_dao.py         # 基础DAO类
│   ├── code_table_dao.py   # 码表DAO
│   └── basic_info_dao.py   # 基本信息DAO
├── ui/                     # 用户界面层
│   ├── __init__.py
│   ├── main_window.py      # 主窗口
│   ├── login_dialog.py     # 登录对话框
│   ├── base_table_widget.py # 基础表格组件
│   ├── code_table_windows.py # 码表管理窗口
│   └── basic_info_windows.py # 基本信息管理窗口
├── utils/                  # 工具模块
│   ├── __init__.py
│   └── logger.py           # 日志工具
├── logs/                   # 日志文件目录
├── main.py                 # 主程序入口
├── requirements.txt        # 依赖包列表
└── README.md              # 项目说明
```

## 安装和运行

### 1. 环境要求
- Python 3.7+
- MySQL 8.0+

### 2. 安装依赖
```bash
pip install -r requirements.txt
```

### 3. 配置数据库
编辑 `config/database.py` 文件，修改数据库连接参数：
```python
self.host = 'localhost'      # 数据库主机
self.port = 3306            # 数据库端口
self.user = 'root'          # 数据库用户名
self.password = 'root'      # 数据库密码
self.database = 'jx_inventory_system'  # 数据库名称
```

### 4. 初始化数据库
```bash
python database/init_database.py
```

### 5. 运行系统
```bash
python main.py
```

### 6. 登录系统
- 默认用户名: `admin`
- 默认密码: `123456`

## 数据库设计

### 主要表结构

#### 码表
- `jx_c_sort` - 商品种类码表
- `jx_c_unit` - 商品计量单位码表
- `jx_c_aero` - 商品产地码表

#### 基本信息表
- `jx_d_dept` - 部门信息表
- `jx_d_operator` - 操作员信息表
- `jx_d_supp` - 往来单位信息表
- `jx_d_goods` - 商品信息表

#### 业务单据表
- `jx_sheet_cg_main/item` - 采购单主表/明细表
- `jx_sheet_xs_main/item` - 销售单主表/明细表
- `jx_sheet_bs_main/item` - 报损单主表/明细表
- `jx_sheet_pc_main/item` - 盘存单主表/明细表
- `jx_sheet_fk` - 付款单
- `jx_sheet_sk` - 收款单

#### 账表
- `jx_table_goods_amount` - 商品库存表
- `jx_table_goods_record` - 流水账表
- `jx_table_yf_mone` - 应付账表
- `jx_table_ys_mone` - 应收账表

#### 会计系统表
- `zw_pz_zb/mxb` - 凭证主表/明细表
- `zw_zb_kmyeb` - 科目余额表
- `zw_yb_zcfzb` - 资产负债表
- `zw_yb_lrb` - 利润表

## 开发计划

### 第一阶段 ✅
- [x] 项目架构设计
- [x] 数据库表结构设计
- [x] 基础数据访问层
- [x] 主界面框架
- [x] 码表管理功能
- [x] 基本信息管理功能

### 第二阶段 🚧
- [ ] 单据管理模块
- [ ] 库存计算与报表模块
- [ ] 成本计算模块

### 第三阶段 📋
- [ ] 会计凭证管理模块
- [ ] 会计账表模块
- [ ] 会计报表模块

### 第四阶段 📋
- [ ] 业财融合处理模块
- [ ] 稽核审计模块
- [ ] 可视化统计分析模块
- [ ] 报表溯源模块

## 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 联系方式

如有问题或建议，请通过以下方式联系：
- 邮箱: [<EMAIL>]
- 项目地址: [https://github.com/your-username/jx-inventory-system]
