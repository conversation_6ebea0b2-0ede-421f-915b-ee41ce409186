# -*- coding: utf-8 -*-
"""
码表管理窗口
"""

from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QFormLayout,
                            QLineEdit, QPushButton, QLabel, QMessageBox,
                            QTableWidgetItem)
from PyQt5.QtCore import Qt
from models import SortDAO, UnitDAO, AeroDAO, DeptDAO
from .base_table_widget import BaseTableWidget
from utils import logger

class CodeTableDialog(QDialog):
    """码表编辑对话框基类"""
    
    def __init__(self, parent=None, title="编辑", data=None):
        super().__init__(parent)
        self.data = data
        self.init_ui(title)
        if data:
            self.load_data()
            
    def init_ui(self, title):
        """初始化UI"""
        self.setWindowTitle(title)
        self.setModal(True)
        self.setFixedSize(400, 200)
        
        layout = QVBoxLayout()
        
        # 表单布局
        form_layout = QFormLayout()
        
        self.code_edit = QLineEdit()
        self.name_edit = QLineEdit()
        
        form_layout.addRow("代码:", self.code_edit)
        form_layout.addRow("名称:", self.name_edit)
        
        layout.addLayout(form_layout)
        
        # 按钮布局
        button_layout = QHBoxLayout()
        
        self.ok_btn = QPushButton("确定")
        self.ok_btn.clicked.connect(self.accept)
        button_layout.addWidget(self.ok_btn)
        
        self.cancel_btn = QPushButton("取消")
        self.cancel_btn.clicked.connect(self.reject)
        button_layout.addWidget(self.cancel_btn)
        
        layout.addLayout(button_layout)
        
        self.setLayout(layout)
        
        # 如果是编辑模式，代码不可修改
        if self.data:
            self.code_edit.setReadOnly(True)
            
    def load_data(self):
        """加载数据（子类重写）"""
        pass
        
    def get_data(self):
        """获取数据"""
        return {
            'code': self.code_edit.text().strip(),
            'name': self.name_edit.text().strip()
        }
        
    def validate_data(self):
        """验证数据"""
        data = self.get_data()
        if not data['code']:
            QMessageBox.warning(self, "提示", "请输入代码！")
            return False
        if not data['name']:
            QMessageBox.warning(self, "提示", "请输入名称！")
            return False
        return True
        
    def accept(self):
        """确定按钮事件"""
        if self.validate_data():
            super().accept()

class SortDialog(CodeTableDialog):
    """商品种类编辑对话框"""
    
    def load_data(self):
        """加载数据"""
        if self.data:
            self.code_edit.setText(self.data.get('sort_code', ''))
            self.name_edit.setText(self.data.get('sort_name', ''))

class UnitDialog(CodeTableDialog):
    """计量单位编辑对话框"""
    
    def load_data(self):
        """加载数据"""
        if self.data:
            self.code_edit.setText(self.data.get('unit_code', ''))
            self.name_edit.setText(self.data.get('unit_name', ''))

class AeroDialog(CodeTableDialog):
    """产地编辑对话框"""
    
    def load_data(self):
        """加载数据"""
        if self.data:
            self.code_edit.setText(self.data.get('aero_code', ''))
            self.name_edit.setText(self.data.get('aero_name', ''))

class DeptDialog(CodeTableDialog):
    """部门编辑对话框"""
    
    def load_data(self):
        """加载数据"""
        if self.data:
            self.code_edit.setText(self.data.get('dept_code', ''))
            self.name_edit.setText(self.data.get('dept_name', ''))

class SortManagementWindow(BaseTableWidget):
    """商品种类管理窗口"""
    
    def __init__(self):
        super().__init__("商品种类管理")
        self.dao = SortDAO()
        self.setup_table()
        self.load_data()
        
    def setup_table(self):
        """设置表格"""
        self.table.setColumnCount(2)
        self.table.setHorizontalHeaderLabels(["种类代码", "种类名称"])
        
        # 设置列宽
        header = self.table.horizontalHeader()
        header.resizeSection(0, 120)
        
    def load_data(self):
        """加载数据"""
        try:
            data = self.dao.get_all_sorts()
            self.table.setRowCount(len(data))
            
            for row, item in enumerate(data):
                self.table.setItem(row, 0, QTableWidgetItem(str(item['sort_code'])))
                self.table.setItem(row, 1, QTableWidgetItem(str(item['sort_name'] or '')))
                
        except Exception as e:
            logger.error(f"加载商品种类数据失败: {e}")
            self.show_message("错误", f"加载数据失败: {str(e)}", "error")
            
    def search_data(self):
        """搜索数据"""
        keyword = self.search_edit.text().strip()
        if not keyword:
            self.load_data()
            return
            
        try:
            # 简单的表格过滤
            for row in range(self.table.rowCount()):
                code_item = self.table.item(row, 0)
                name_item = self.table.item(row, 1)
                
                code_text = code_item.text() if code_item else ""
                name_text = name_item.text() if name_item else ""
                
                if keyword.lower() in code_text.lower() or keyword.lower() in name_text.lower():
                    self.table.setRowHidden(row, False)
                else:
                    self.table.setRowHidden(row, True)
                    
        except Exception as e:
            logger.error(f"搜索商品种类失败: {e}")
            
    def add_item(self):
        """添加商品种类"""
        dialog = SortDialog(self, "添加商品种类")
        if dialog.exec_() == dialog.Accepted:
            data = dialog.get_data()
            try:
                if self.dao.sort_exists(data['code']):
                    self.show_message("提示", "种类代码已存在！", "warning")
                    return
                    
                if self.dao.add_sort(data['code'], data['name']):
                    self.show_message("成功", "添加成功！")
                    self.load_data()
                else:
                    self.show_message("错误", "添加失败！", "error")
                    
            except Exception as e:
                logger.error(f"添加商品种类失败: {e}")
                self.show_message("错误", f"添加失败: {str(e)}", "error")
                
    def edit_item(self):
        """编辑商品种类"""
        if self.current_row < 0:
            return
            
        data = self.get_selected_data()
        if not data:
            return
            
        dialog = SortDialog(self, "修改商品种类", data)
        if dialog.exec_() == dialog.Accepted:
            new_data = dialog.get_data()
            try:
                if self.dao.update_sort(new_data['code'], new_data['name']):
                    self.show_message("成功", "修改成功！")
                    self.load_data()
                else:
                    self.show_message("错误", "修改失败！", "error")
                    
            except Exception as e:
                logger.error(f"修改商品种类失败: {e}")
                self.show_message("错误", f"修改失败: {str(e)}", "error")
                
    def delete_item(self):
        """删除商品种类"""
        if self.current_row < 0:
            return
            
        data = self.get_selected_data()
        if not data:
            return
            
        if self.confirm_delete(data['sort_name']):
            try:
                if self.dao.delete_sort(data['sort_code']):
                    self.show_message("成功", "删除成功！")
                    self.load_data()
                else:
                    self.show_message("错误", "删除失败！", "error")
                    
            except Exception as e:
                logger.error(f"删除商品种类失败: {e}")
                self.show_message("错误", f"删除失败: {str(e)}", "error")
                
    def get_selected_data(self):
        """获取选中行的数据"""
        if self.current_row < 0:
            return None
            
        code_item = self.table.item(self.current_row, 0)
        name_item = self.table.item(self.current_row, 1)
        
        if not code_item:
            return None
            
        return {
            'sort_code': code_item.text(),
            'sort_name': name_item.text() if name_item else ''
        }

class UnitManagementWindow(BaseTableWidget):
    """计量单位管理窗口"""

    def __init__(self):
        super().__init__("计量单位管理")
        self.dao = UnitDAO()
        self.setup_table()
        self.load_data()

    def setup_table(self):
        """设置表格"""
        self.table.setColumnCount(2)
        self.table.setHorizontalHeaderLabels(["单位代码", "单位名称"])

        # 设置列宽
        header = self.table.horizontalHeader()
        header.resizeSection(0, 120)

    def load_data(self):
        """加载数据"""
        try:
            data = self.dao.get_all_units()
            self.table.setRowCount(len(data))

            for row, item in enumerate(data):
                self.table.setItem(row, 0, QTableWidgetItem(str(item['unit_code'])))
                self.table.setItem(row, 1, QTableWidgetItem(str(item['unit_name'] or '')))

        except Exception as e:
            logger.error(f"加载计量单位数据失败: {e}")
            self.show_message("错误", f"加载数据失败: {str(e)}", "error")

    def search_data(self):
        """搜索数据"""
        keyword = self.search_edit.text().strip()
        if not keyword:
            self.load_data()
            return

        try:
            for row in range(self.table.rowCount()):
                code_item = self.table.item(row, 0)
                name_item = self.table.item(row, 1)

                code_text = code_item.text() if code_item else ""
                name_text = name_item.text() if name_item else ""

                if keyword.lower() in code_text.lower() or keyword.lower() in name_text.lower():
                    self.table.setRowHidden(row, False)
                else:
                    self.table.setRowHidden(row, True)

        except Exception as e:
            logger.error(f"搜索计量单位失败: {e}")

    def add_item(self):
        """添加计量单位"""
        dialog = UnitDialog(self, "添加计量单位")
        if dialog.exec_() == dialog.Accepted:
            data = dialog.get_data()
            try:
                if self.dao.unit_exists(data['code']):
                    self.show_message("提示", "单位代码已存在！", "warning")
                    return

                if self.dao.add_unit(data['code'], data['name']):
                    self.show_message("成功", "添加成功！")
                    self.load_data()
                else:
                    self.show_message("错误", "添加失败！", "error")

            except Exception as e:
                logger.error(f"添加计量单位失败: {e}")
                self.show_message("错误", f"添加失败: {str(e)}", "error")

    def edit_item(self):
        """编辑计量单位"""
        if self.current_row < 0:
            return

        data = self.get_selected_data()
        if not data:
            return

        dialog = UnitDialog(self, "修改计量单位", data)
        if dialog.exec_() == dialog.Accepted:
            new_data = dialog.get_data()
            try:
                if self.dao.update_unit(new_data['code'], new_data['name']):
                    self.show_message("成功", "修改成功！")
                    self.load_data()
                else:
                    self.show_message("错误", "修改失败！", "error")

            except Exception as e:
                logger.error(f"修改计量单位失败: {e}")
                self.show_message("错误", f"修改失败: {str(e)}", "error")

    def delete_item(self):
        """删除计量单位"""
        if self.current_row < 0:
            return

        data = self.get_selected_data()
        if not data:
            return

        if self.confirm_delete(data['unit_name']):
            try:
                if self.dao.delete_unit(data['unit_code']):
                    self.show_message("成功", "删除成功！")
                    self.load_data()
                else:
                    self.show_message("错误", "删除失败！", "error")

            except Exception as e:
                logger.error(f"删除计量单位失败: {e}")
                self.show_message("错误", f"删除失败: {str(e)}", "error")

    def get_selected_data(self):
        """获取选中行的数据"""
        if self.current_row < 0:
            return None

        code_item = self.table.item(self.current_row, 0)
        name_item = self.table.item(self.current_row, 1)

        if not code_item:
            return None

        return {
            'unit_code': code_item.text(),
            'unit_name': name_item.text() if name_item else ''
        }

class AeroManagementWindow(BaseTableWidget):
    """产地管理窗口"""

    def __init__(self):
        super().__init__("产地管理")
        self.dao = AeroDAO()
        self.setup_table()
        self.load_data()

    def setup_table(self):
        """设置表格"""
        self.table.setColumnCount(2)
        self.table.setHorizontalHeaderLabels(["产地代码", "产地名称"])

        # 设置列宽
        header = self.table.horizontalHeader()
        header.resizeSection(0, 120)

    def load_data(self):
        """加载数据"""
        try:
            data = self.dao.get_all_aeros()
            self.table.setRowCount(len(data))

            for row, item in enumerate(data):
                self.table.setItem(row, 0, QTableWidgetItem(str(item['aero_code'])))
                self.table.setItem(row, 1, QTableWidgetItem(str(item['aero_name'] or '')))

        except Exception as e:
            logger.error(f"加载产地数据失败: {e}")
            self.show_message("错误", f"加载数据失败: {str(e)}", "error")

    def search_data(self):
        """搜索数据"""
        keyword = self.search_edit.text().strip()
        if not keyword:
            self.load_data()
            return

        try:
            for row in range(self.table.rowCount()):
                code_item = self.table.item(row, 0)
                name_item = self.table.item(row, 1)

                code_text = code_item.text() if code_item else ""
                name_text = name_item.text() if name_item else ""

                if keyword.lower() in code_text.lower() or keyword.lower() in name_text.lower():
                    self.table.setRowHidden(row, False)
                else:
                    self.table.setRowHidden(row, True)

        except Exception as e:
            logger.error(f"搜索产地失败: {e}")

    def add_item(self):
        """添加产地"""
        dialog = AeroDialog(self, "添加产地")
        if dialog.exec_() == dialog.Accepted:
            data = dialog.get_data()
            try:
                if self.dao.aero_exists(data['code']):
                    self.show_message("提示", "产地代码已存在！", "warning")
                    return

                if self.dao.add_aero(data['code'], data['name']):
                    self.show_message("成功", "添加成功！")
                    self.load_data()
                else:
                    self.show_message("错误", "添加失败！", "error")

            except Exception as e:
                logger.error(f"添加产地失败: {e}")
                self.show_message("错误", f"添加失败: {str(e)}", "error")

    def edit_item(self):
        """编辑产地"""
        if self.current_row < 0:
            return

        data = self.get_selected_data()
        if not data:
            return

        dialog = AeroDialog(self, "修改产地", data)
        if dialog.exec_() == dialog.Accepted:
            new_data = dialog.get_data()
            try:
                if self.dao.update_aero(new_data['code'], new_data['name']):
                    self.show_message("成功", "修改成功！")
                    self.load_data()
                else:
                    self.show_message("错误", "修改失败！", "error")

            except Exception as e:
                logger.error(f"修改产地失败: {e}")
                self.show_message("错误", f"修改失败: {str(e)}", "error")

    def delete_item(self):
        """删除产地"""
        if self.current_row < 0:
            return

        data = self.get_selected_data()
        if not data:
            return

        if self.confirm_delete(data['aero_name']):
            try:
                if self.dao.delete_aero(data['aero_code']):
                    self.show_message("成功", "删除成功！")
                    self.load_data()
                else:
                    self.show_message("错误", "删除失败！", "error")

            except Exception as e:
                logger.error(f"删除产地失败: {e}")
                self.show_message("错误", f"删除失败: {str(e)}", "error")

    def get_selected_data(self):
        """获取选中行的数据"""
        if self.current_row < 0:
            return None

        code_item = self.table.item(self.current_row, 0)
        name_item = self.table.item(self.current_row, 1)

        if not code_item:
            return None

        return {
            'aero_code': code_item.text(),
            'aero_name': name_item.text() if name_item else ''
        }

class DeptManagementWindow(BaseTableWidget):
    """部门管理窗口"""

    def __init__(self):
        super().__init__("部门管理")
        self.dao = DeptDAO()
        self.setup_table()
        self.load_data()

    def setup_table(self):
        """设置表格"""
        self.table.setColumnCount(2)
        self.table.setHorizontalHeaderLabels(["部门代码", "部门名称"])

        # 设置列宽
        header = self.table.horizontalHeader()
        header.resizeSection(0, 120)

    def load_data(self):
        """加载数据"""
        try:
            data = self.dao.get_all_depts()
            self.table.setRowCount(len(data))

            for row, item in enumerate(data):
                self.table.setItem(row, 0, QTableWidgetItem(str(item['dept_code'])))
                self.table.setItem(row, 1, QTableWidgetItem(str(item['dept_name'] or '')))

        except Exception as e:
            logger.error(f"加载部门数据失败: {e}")
            self.show_message("错误", f"加载数据失败: {str(e)}", "error")

    def search_data(self):
        """搜索数据"""
        keyword = self.search_edit.text().strip()
        if not keyword:
            self.load_data()
            return

        try:
            for row in range(self.table.rowCount()):
                code_item = self.table.item(row, 0)
                name_item = self.table.item(row, 1)

                code_text = code_item.text() if code_item else ""
                name_text = name_item.text() if name_item else ""

                if keyword.lower() in code_text.lower() or keyword.lower() in name_text.lower():
                    self.table.setRowHidden(row, False)
                else:
                    self.table.setRowHidden(row, True)

        except Exception as e:
            logger.error(f"搜索部门失败: {e}")

    def add_item(self):
        """添加部门"""
        dialog = DeptDialog(self, "添加部门")
        if dialog.exec_() == dialog.Accepted:
            data = dialog.get_data()
            try:
                if self.dao.dept_exists(data['code']):
                    self.show_message("提示", "部门代码已存在！", "warning")
                    return

                if self.dao.add_dept(data['code'], data['name']):
                    self.show_message("成功", "添加成功！")
                    self.load_data()
                else:
                    self.show_message("错误", "添加失败！", "error")

            except Exception as e:
                logger.error(f"添加部门失败: {e}")
                self.show_message("错误", f"添加失败: {str(e)}", "error")

    def edit_item(self):
        """编辑部门"""
        if self.current_row < 0:
            return

        data = self.get_selected_data()
        if not data:
            return

        dialog = DeptDialog(self, "修改部门", data)
        if dialog.exec_() == dialog.Accepted:
            new_data = dialog.get_data()
            try:
                if self.dao.update_dept(new_data['code'], new_data['name']):
                    self.show_message("成功", "修改成功！")
                    self.load_data()
                else:
                    self.show_message("错误", "修改失败！", "error")

            except Exception as e:
                logger.error(f"修改部门失败: {e}")
                self.show_message("错误", f"修改失败: {str(e)}", "error")

    def delete_item(self):
        """删除部门"""
        if self.current_row < 0:
            return

        data = self.get_selected_data()
        if not data:
            return

        if self.confirm_delete(data['dept_name']):
            try:
                if self.dao.delete_dept(data['dept_code']):
                    self.show_message("成功", "删除成功！")
                    self.load_data()
                else:
                    self.show_message("错误", "删除失败！", "error")

            except Exception as e:
                logger.error(f"删除部门失败: {e}")
                self.show_message("错误", f"删除失败: {str(e)}", "error")

    def get_selected_data(self):
        """获取选中行的数据"""
        if self.current_row < 0:
            return None

        code_item = self.table.item(self.current_row, 0)
        name_item = self.table.item(self.current_row, 1)

        if not code_item:
            return None

        return {
            'dept_code': code_item.text(),
            'dept_name': name_item.text() if name_item else ''
        }
