# -*- coding: utf-8 -*-
"""
登录对话框
"""

from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QFormLayout,
                            QLineEdit, QPushButton, QLabel, QMessageBox,
                            QFrame, QSpacerItem, QSizePolicy, QCheckBox,
                            QGraphicsDropShadowEffect, QApplication)
from PyQt5.QtCore import Qt, QTimer, pyqtSignal, QPropertyAnimation, QEasingCurve
from PyQt5.QtGui import QFont, QPixmap, QPalette, QIcon, QColor, QPainter, QLinearGradient
from models import OperatorDAO
from utils import logger
import sys

class ForgotPasswordDialog(QDialog):
    """忘记密码对话框"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.init_ui()

    def init_ui(self):
        """初始化UI"""
        self.setWindowTitle("忘记密码")
        self.setFixedSize(400, 250)
        self.setModal(True)

        # 设置样式
        self.setStyleSheet("""
            QDialog {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #667eea, stop:1 #764ba2);
                border-radius: 10px;
            }
            QLabel {
                color: white;
                font-size: 16px;
                font-family: "Microsoft YaHei";
            }
            QLineEdit {
                padding: 10px;
                border: 2px solid rgba(255,255,255,0.3);
                border-radius: 8px;
                background: rgba(255,255,255,0.9);
                font-size: 14px;
            }
            QPushButton {
                padding: 12px 25px;
                border: none;
                border-radius: 8px;
                font-size: 16px;
                font-weight: bold;
                color: white;
                background: rgba(255,255,255,0.2);
                font-family: "Microsoft YaHei";
            }
            QPushButton:hover {
                background: rgba(255,255,255,0.3);
            }
        """)

        layout = QVBoxLayout()
        layout.setSpacing(20)
        layout.setContentsMargins(40, 30, 40, 30)

        # 标题
        title = QLabel("密码找回")
        title.setAlignment(Qt.AlignCenter)
        title.setFont(QFont("Microsoft YaHei", 18, QFont.Bold))
        layout.addWidget(title)

        # 说明文字
        info_label = QLabel("请联系系统管理员重置密码\n\n联系方式：\n电话：400-123-4567\n邮箱：<EMAIL>")
        info_label.setAlignment(Qt.AlignCenter)
        info_label.setWordWrap(True)
        layout.addWidget(info_label)

        # 按钮
        btn_layout = QHBoxLayout()

        self.ok_btn = QPushButton("确定")
        self.ok_btn.clicked.connect(self.accept)
        btn_layout.addWidget(self.ok_btn)

        layout.addLayout(btn_layout)
        self.setLayout(layout)

class LoginDialog(QDialog):
    """登录对话框类"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.user_info = None
        self.operator_dao = OperatorDAO()
        self.login_attempts = 0
        self.max_attempts = 3
        self.init_ui()
        self.setup_animations()
        
    def init_ui(self):
        """初始化UI"""
        self.setWindowTitle("JX进销存管理信息系统 - 用户登录")
        self.setFixedSize(460, 400)
        self.setModal(True)
        self.setWindowFlags(Qt.Dialog | Qt.FramelessWindowHint)

        # 设置窗口样式
        self.setStyleSheet("""
            QDialog {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #667eea, stop:1 #764ba2);
                border-radius: 15px;
            }
            QLabel#title {
                color: white;
                font-size: 22px;
                font-weight: bold;
                margin: 8px 0;
                font-family: "Microsoft YaHei";
            }
            QLabel#subtitle {
                color: rgba(255,255,255,0.9);
                font-size: 13px;
                margin-bottom: 15px;
                font-family: "Microsoft YaHei";
            }
            QLabel {
                color: white;
                font-size: 15px;
                font-weight: bold;
                font-family: "Microsoft YaHei";
                margin-top: 5px;
                margin-bottom: 3px;
            }
            QLineEdit {
                padding: 12px 15px;
                border: 2px solid rgba(255,255,255,0.3);
                border-radius: 8px;
                font-size: 15px;
                background: rgba(255,255,255,0.95);
                color: #2c3e50;
                font-family: "Microsoft YaHei";
                font-weight: normal;
                min-height: 18px;
            }
            QLineEdit:focus {
                border-color: rgba(255,255,255,0.8);
                background: white;
            }
            QLineEdit::placeholder {
                color: #7f8c8d;
                font-size: 14px;
            }
            QPushButton {
                padding: 10px 20px;
                border: none;
                border-radius: 8px;
                font-size: 14px;
                font-weight: bold;
                color: white;
                min-height: 18px;
                font-family: "Microsoft YaHei";
            }
            QPushButton#login_btn {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #ff6b6b, stop:1 #ee5a24);
            }
            QPushButton#login_btn:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #ff5252, stop:1 #d63031);
            }
            QPushButton#cancel_btn {
                background: rgba(255,255,255,0.2);
                border: 2px solid rgba(255,255,255,0.3);
            }
            QPushButton#cancel_btn:hover {
                background: rgba(255,255,255,0.3);
            }
            QPushButton#forgot_btn {
                background: transparent;
                color: rgba(255,255,255,0.8);
                text-decoration: underline;
                padding: 5px;
                font-size: 12px;
                font-family: "Microsoft YaHei";
            }
            QPushButton#forgot_btn:hover {
                color: white;
            }
            QCheckBox {
                color: white;
                font-size: 12px;
                font-family: "Microsoft YaHei";
            }
            QCheckBox::indicator {
                width: 18px;
                height: 18px;
                border-radius: 3px;
                border: 2px solid rgba(255,255,255,0.5);
                background: rgba(255,255,255,0.1);
            }
            QCheckBox::indicator:checked {
                background: rgba(255,255,255,0.8);
                border-color: white;
            }
        """)
        
        # 添加阴影效果
        shadow = QGraphicsDropShadowEffect()
        shadow.setBlurRadius(20)
        shadow.setColor(QColor(0, 0, 0, 80))
        shadow.setOffset(0, 10)
        self.setGraphicsEffect(shadow)

        # 主布局
        main_layout = QVBoxLayout()
        main_layout.setSpacing(18)
        main_layout.setContentsMargins(40, 30, 40, 30)

        # 关闭按钮
        close_btn_layout = QHBoxLayout()
        close_btn_layout.addStretch()
        self.close_btn = QPushButton("×")
        self.close_btn.setFixedSize(30, 30)
        self.close_btn.setStyleSheet("""
            QPushButton {
                background: rgba(255,255,255,0.2);
                border: none;
                border-radius: 15px;
                color: white;
                font-size: 18px;
                font-weight: bold;
            }
            QPushButton:hover {
                background: rgba(255,255,255,0.3);
            }
        """)
        self.close_btn.clicked.connect(self.reject)
        close_btn_layout.addWidget(self.close_btn)
        main_layout.addLayout(close_btn_layout)

        # 标题区域
        title_layout = QVBoxLayout()
        title_layout.setSpacing(3)

        title_label = QLabel("欢迎登录")
        title_label.setObjectName("title")
        title_label.setAlignment(Qt.AlignCenter)
        title_layout.addWidget(title_label)

        subtitle_label = QLabel("JX进销存管理信息系统")
        subtitle_label.setObjectName("subtitle")
        subtitle_label.setAlignment(Qt.AlignCenter)
        title_layout.addWidget(subtitle_label)

        main_layout.addLayout(title_layout)
        
        # 表单布局
        form_layout = QVBoxLayout()
        form_layout.setSpacing(8)

        # 用户名输入
        username_label = QLabel("用户名")
        form_layout.addWidget(username_label)

        self.username_edit = QLineEdit()
        self.username_edit.setPlaceholderText("请输入用户名")
        self.username_edit.setText("admin")  # 默认用户名
        form_layout.addWidget(self.username_edit)

        # 添加间距
        form_layout.addSpacing(10)

        # 密码输入
        password_label = QLabel("密码")
        form_layout.addWidget(password_label)

        self.password_edit = QLineEdit()
        self.password_edit.setPlaceholderText("请输入密码")
        self.password_edit.setEchoMode(QLineEdit.Password)
        self.password_edit.setText("123456")  # 默认密码
        form_layout.addWidget(self.password_edit)

        # 记住密码选项
        options_layout = QHBoxLayout()
        self.remember_checkbox = QCheckBox("记住密码")
        options_layout.addWidget(self.remember_checkbox)

        options_layout.addStretch()

        # 忘记密码按钮
        self.forgot_btn = QPushButton("忘记密码？")
        self.forgot_btn.setObjectName("forgot_btn")
        self.forgot_btn.clicked.connect(self.show_forgot_password)
        options_layout.addWidget(self.forgot_btn)

        form_layout.addLayout(options_layout)
        main_layout.addLayout(form_layout)
        
        # 按钮布局
        button_layout = QVBoxLayout()
        button_layout.setSpacing(10)

        # 登录按钮
        self.login_btn = QPushButton("登 录")
        self.login_btn.setObjectName("login_btn")
        self.login_btn.clicked.connect(self.login)
        self.login_btn.setDefault(True)
        self.login_btn.setFixedHeight(38)
        button_layout.addWidget(self.login_btn)

        # 取消按钮
        self.cancel_btn = QPushButton("取 消")
        self.cancel_btn.setObjectName("cancel_btn")
        self.cancel_btn.clicked.connect(self.reject)
        self.cancel_btn.setFixedHeight(35)
        button_layout.addWidget(self.cancel_btn)

        main_layout.addLayout(button_layout)

        # 错误提示标签
        self.error_label = QLabel("")
        self.error_label.setAlignment(Qt.AlignCenter)
        self.error_label.setStyleSheet("""
            color: #ff6b6b;
            font-size: 12px;
            margin-top: 8px;
            font-family: "Microsoft YaHei";
            background: rgba(255,255,255,0.1);
            padding: 6px;
            border-radius: 4px;
        """)
        self.error_label.hide()
        main_layout.addWidget(self.error_label)
        
        self.setLayout(main_layout)

        # 设置回车键登录
        self.username_edit.returnPressed.connect(self.login)
        self.password_edit.returnPressed.connect(self.login)

    def setup_animations(self):
        """设置动画效果"""
        # 窗口淡入动画
        self.setWindowOpacity(0)
        self.fade_animation = QPropertyAnimation(self, b"windowOpacity")
        self.fade_animation.setDuration(500)
        self.fade_animation.setStartValue(0)
        self.fade_animation.setEndValue(1)
        self.fade_animation.setEasingCurve(QEasingCurve.OutCubic)

        # 启动动画
        QTimer.singleShot(100, self.fade_animation.start)

    def show_forgot_password(self):
        """显示忘记密码对话框"""
        dialog = ForgotPasswordDialog(self)
        dialog.exec_()

    def show_error(self, message):
        """显示错误信息"""
        self.error_label.setText(message)
        self.error_label.show()

        # 错误信息3秒后自动隐藏
        QTimer.singleShot(3000, self.error_label.hide)

        # 输入框抖动效果
        self.shake_widget(self.password_edit)

    def shake_widget(self, widget):
        """控件抖动效果"""
        self.shake_animation = QPropertyAnimation(widget, b"geometry")
        self.shake_animation.setDuration(500)

        original_geometry = widget.geometry()

        # 设置抖动关键帧
        self.shake_animation.setKeyValueAt(0, original_geometry)
        self.shake_animation.setKeyValueAt(0.1, original_geometry.translated(5, 0))
        self.shake_animation.setKeyValueAt(0.2, original_geometry.translated(-5, 0))
        self.shake_animation.setKeyValueAt(0.3, original_geometry.translated(5, 0))
        self.shake_animation.setKeyValueAt(0.4, original_geometry.translated(-5, 0))
        self.shake_animation.setKeyValueAt(0.5, original_geometry.translated(3, 0))
        self.shake_animation.setKeyValueAt(0.6, original_geometry.translated(-3, 0))
        self.shake_animation.setKeyValueAt(0.7, original_geometry.translated(2, 0))
        self.shake_animation.setKeyValueAt(0.8, original_geometry.translated(-2, 0))
        self.shake_animation.setKeyValueAt(0.9, original_geometry.translated(1, 0))
        self.shake_animation.setKeyValueAt(1, original_geometry)

        self.shake_animation.start()
        
    def login(self):
        """登录验证"""
        username = self.username_edit.text().strip()
        password = self.password_edit.text().strip()

        # 隐藏之前的错误信息
        self.error_label.hide()

        if not username:
            self.show_error("请输入用户名！")
            self.username_edit.setFocus()
            return

        if not password:
            self.show_error("请输入密码！")
            self.password_edit.setFocus()
            return

        try:
            # 验证用户登录
            user_info = self.operator_dao.validate_login(username, password)

            if user_info:
                self.user_info = user_info
                logger.info(f"用户 {username} 登录成功")

                # 登录成功动画效果
                self.login_btn.setText("登录成功！")
                self.login_btn.setStyleSheet("""
                    QPushButton {
                        background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                            stop:0 #00b894, stop:1 #00a085);
                        padding: 12px 25px;
                        border: none;
                        border-radius: 10px;
                        font-size: 14px;
                        font-weight: bold;
                        color: white;
                        min-height: 20px;
                    }
                """)

                # 延迟关闭对话框
                QTimer.singleShot(1000, self.accept)
            else:
                self.login_attempts += 1
                remaining_attempts = self.max_attempts - self.login_attempts

                if remaining_attempts > 0:
                    self.show_error(f"用户名或密码错误！还有 {remaining_attempts} 次尝试机会")
                    self.password_edit.clear()
                    self.password_edit.setFocus()
                else:
                    # 超过最大尝试次数，关闭程序
                    self.show_error("登录失败次数过多，程序将退出！")
                    QTimer.singleShot(2000, self.close_application)

        except Exception as e:
            logger.error(f"登录验证失败: {e}")
            self.show_error(f"登录验证失败: {str(e)}")

    def close_application(self):
        """关闭应用程序"""
        logger.warning("用户登录失败次数过多，程序退出")
        QApplication.instance().quit()
        sys.exit(1)
    
    def get_user_info(self):
        """获取用户信息"""
        return self.user_info
