# -*- coding: utf-8 -*-
"""
数据库初始化脚本
"""

import pymysql
import os
import sys

# 添加项目根目录到路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

from config.database import db_config
from utils.logger import logger

def read_sql_file(file_path):
    """读取SQL文件"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            return f.read()
    except Exception as e:
        logger.error(f"读取SQL文件失败: {e}")
        return None

def execute_sql_script(connection, sql_script):
    """执行SQL脚本"""
    try:
        # 更好的SQL分割方法
        statements = []
        current_statement = ""

        for line in sql_script.split('\n'):
            line = line.strip()
            if not line or line.startswith('--'):
                continue

            current_statement += line + " "

            if line.endswith(';'):
                statements.append(current_statement.strip())
                current_statement = ""

        with connection.cursor() as cursor:
            for statement in statements:
                if statement:
                    try:
                        cursor.execute(statement)
                        logger.info(f"执行SQL: {statement[:50]}...")
                    except Exception as e:
                        logger.warning(f"执行SQL失败: {e}, SQL: {statement[:100]}")

        connection.commit()
        return True

    except Exception as e:
        logger.error(f"执行SQL脚本失败: {e}")
        connection.rollback()
        return False

def create_database():
    """创建数据库"""
    try:
        # 连接MySQL服务器（不指定数据库）
        connection = pymysql.connect(
            host=db_config.host,
            port=db_config.port,
            user=db_config.user,
            password=db_config.password,
            charset=db_config.charset,
            autocommit=False
        )
        
        with connection.cursor() as cursor:
            # 创建数据库
            cursor.execute(f"CREATE DATABASE IF NOT EXISTS {db_config.database} DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci")
            logger.info(f"数据库 {db_config.database} 创建成功")
            
        connection.commit()
        connection.close()
        return True
        
    except Exception as e:
        logger.error(f"创建数据库失败: {e}")
        return False

def init_database():
    """初始化数据库"""
    logger.info("开始初始化数据库...")
    
    # 1. 创建数据库
    if not create_database():
        logger.error("创建数据库失败")
        return False
    
    # 2. 获取数据库连接
    connection = db_config.get_connection()
    if not connection:
        logger.error("获取数据库连接失败")
        return False
    
    try:
        # 3. 执行建表脚本
        logger.info("执行建表脚本...")
        create_tables_sql = read_sql_file('database/create_tables.sql')
        if not create_tables_sql:
            logger.error("读取建表脚本失败")
            return False
            
        if not execute_sql_script(connection, create_tables_sql):
            logger.error("执行建表脚本失败")
            return False
        
        logger.info("建表脚本执行成功")
        
        # 4. 执行初始化数据脚本
        logger.info("执行初始化数据脚本...")
        init_data_sql = read_sql_file('database/init_data.sql')
        if not init_data_sql:
            logger.error("读取初始化数据脚本失败")
            return False
            
        if not execute_sql_script(connection, init_data_sql):
            logger.error("执行初始化数据脚本失败")
            return False
        
        logger.info("初始化数据脚本执行成功")
        
        logger.info("数据库初始化完成！")
        return True
        
    except Exception as e:
        logger.error(f"数据库初始化失败: {e}")
        return False
        
    finally:
        if connection:
            connection.close()

def main():
    """主函数"""
    print("JX进销存管理信息系统 - 数据库初始化工具")
    print("=" * 50)
    
    # 检查数据库配置
    print(f"数据库主机: {db_config.host}:{db_config.port}")
    print(f"数据库名称: {db_config.database}")
    print(f"用户名: {db_config.user}")
    print()
    
    # 确认初始化
    confirm = input("确定要初始化数据库吗？这将删除现有数据！(y/N): ")
    if confirm.lower() != 'y':
        print("取消初始化")
        return
    
    # 执行初始化
    if init_database():
        print("\n数据库初始化成功！")
        print("现在可以运行主程序了：python main.py")
    else:
        print("\n数据库初始化失败！请检查日志文件。")

if __name__ == "__main__":
    main()
